package cn.com.sinosoft.os.beijingexitapply.service;

import cn.com.sinosoft.os.beijingexitapply.model.BeijingExitApply;

/**
 * 出京申请 - service接口.
 *
 * <AUTHOR>
 * @date: 2024/01/01 10:00:00
 * @version V1.0
 */
public interface BeijingExitApplyService {

    /**
     * 出京申请 - 获取单条数据.
     *
     * <AUTHOR>
     * @param id 主键
     * @return 出京申请 数据
     */
    BeijingExitApply get(String id);

    /**
     * 出京申请 - 删除.
     *
     * <AUTHOR>
     * @param id 主键
     */
    void delete(String id);

    /**
     * 出京申请 - 保存.
     *
     * <AUTHOR>
     * @param result 出京申请 数据
     */
    void save(BeijingExitApply result);

    /**
     * 出京申请 - 修改.
     *
     * <AUTHOR>
     * @param result 出京申请 数据
     */
    void edit(BeijingExitApply result);

    /**
     * 出京申请 - 根据流程实例ID获取数据.
     *
     * <AUTHOR>
     * @param piId 流程实例ID
     * @return 出京申请 数据
     */
    BeijingExitApply getByPiId(String piId);

    /**
     * 出京申请 - 启动工作流.
     *
     * <AUTHOR>
     * @param result 出京申请 数据
     * @return 流程实例ID
     */
    String startWorkflow(BeijingExitApply result);

    /**
     * 出京申请 - 完成任务.
     *
     * <AUTHOR>
     * @param taskId 任务ID
     * @param result 出京申请 数据
     */
    void completeTask(String taskId, BeijingExitApply result);

    /**
     * 出京申请 - 驳回任务.
     *
     * <AUTHOR>
     * @param taskId 任务ID
     * @param result 出京申请 数据
     */
    void rejectTask(String taskId, BeijingExitApply result);

    /**
     *
     * @Title: getPiId
     * @Description: 通过piId获取实体类
     * @author: wxm
     * @param:  piId 流程标识
     * @return: BeijingExitApply
     * @exception：
     */
    BeijingExitApply getPiId(String piId);

    /**
     *
     * @Title: applySubmit
     * @Description: 流程办理
     * @author: wxm
     * @param:  参数详见方法内部参数
     * @return: void
     * @exception：
     */
    void applySubmit(BeijingExitApply result, String parama, String taskid, String piId);

    /**
     *
     * @Title: revokeApply
     * @Description: 撤销申请
     * @author: wxm
     * @param:  id 主键
     * @return: void
     * @exception：
     */
    void revokeApply(String id);

    /**
     *
     * @Title: submitApply
     * @Description: 提交申请
     * @author: wxm
     * @param:  id 提交主键
     * @return: void
     * @exception：
     */
    void submitApply(String id);

}
