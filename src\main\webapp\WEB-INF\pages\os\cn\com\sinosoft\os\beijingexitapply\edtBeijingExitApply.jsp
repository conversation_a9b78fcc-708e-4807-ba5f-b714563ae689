<%@ page language="java" contentType="text/html; charset=GBK"
	pageEncoding="GBK"%>
<%@ page import="ie.weaf.toolkit.Util"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>����������� - ά��</title>
<meta http-equiv="content-type" content="text/html; charset=GBK" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<%@ include file="/common/taglibs.jsp"%>
<script type="text/javascript">
	var form;
	$(document).ready(function() {
		mini.parse();
		form = new mini.Form("form1");
		
		// ���������������
		calculateTravelDays();
	});
	
	// ���������������
	function calculateTravelDays() {
		var startDate = mini.get("startDate").getValue();
		var endDate = mini.get("endDate").getValue();
		
		if (startDate && endDate) {
			var start = new Date(startDate);
			var end = new Date(endDate);
			var timeDiff = end.getTime() - start.getTime();
			var dayDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
			
			if (dayDiff > 0) {
				mini.get("travelDays").setValue(dayDiff);
			}
		}
	}
	
	//����
	function save(e) {
		form.validate();
		if (form.isValid() == false) {
			//��ʾ��֤������Ϣ��400Ϊ��ʾ����ȣ�300Ϊ��ʾ��߶�
			showFormErrorTexts(form.getErrorTexts(), 400, 300);
			form.getErrors()[0].focus();
			return;
		}
		mini.get("saveType").setValue(e);//�ύ��ʽ
		if ("${pageState}" == "add") {
			document.form1.action = "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_addParentSubmit.ac";
		} else {
			document.form1.action = "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_edtParentSubmit.ac";
		}
		document.form1.submit();
		waitClick();
	}
	
	//����˴���
	function toSubmit(type) {
		mini.get("auditResult").setValue(type);
		if ("1" == type) {
			mini.get("auditOpinion").setValue("ͬ��");
		} else {
			mini.get("auditOpinion").setValue("��ͬ��");
		}
		var win = mini.get("win1");
		//��ʾ���window
		win.showAtPos("center", "middle");
	}
	
	//ȷ���ύ
	function onOk() {
		var auditOpinion = mini.get("auditOpinion").getValue();
		if (!auditOpinion) {
			mini.alert("����д������������");
			return;
		}
		
		document.form1.action = "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_auditSubmit.ac";
		document.form1.submit();
		waitClick();
	}
	
	//ȡ��
	function onCancel() {
		var win = mini.get("win1");
		win.hide();
	}
</script>
</head>
<body>
	<div class="mini-fit">
		<div class="mini-toolbar" style="border-bottom: 0; padding: 0px;">
			<table style="width: 100%;">
				<tr>
					<td style="width: 100%;">
						<a class="mini-button" iconCls="icon-save" onclick="save('0')" plain="true">����</a>
						<a class="mini-button" iconCls="icon-upload" onclick="save('1')" plain="true">�ύ</a>
						<span class="separator"></span>
						<c:if test="${parama != '0' && pageState == 'view'}">
							<a class="mini-button" iconCls="icon-ok" onclick="toSubmit('1')" plain="true">ͬ��</a>
							<a class="mini-button" iconCls="icon-cancel" onclick="toSubmit('0')" plain="true">����</a>
							<span class="separator"></span>
						</c:if>
						<a class="mini-button" iconCls="icon-close" onclick="CloseWindow('reload')" plain="true">�ر�</a>
					</td>
				</tr>
			</table>
		</div>
		
		<div class="mini-fit">
			<form id="form1" method="post">
				<input name="id" class="mini-hidden" value="${result.id}" />
				<input name="piId" class="mini-hidden" value="${result.piId}" />
				<input name="taskid" class="mini-hidden" value="${taskid}" />
				<input name="parama" class="mini-hidden" value="${parama}" />
				<input id="saveType" name="saveType" class="mini-hidden" />
				<input id="auditResult" name="auditResult" class="mini-hidden" />
				
				<div class="form-container3">
					<table class="form-table">
						<tr>
							<td class="form-label">����������</td>
							<td class="form-content">
								<input name="result.applicantName" class="mini-textbox" value="${result.applicantName}" 
									required="true" requiredErrorText="������������Ϊ��" style="width:150px;" 
									<c:if test="${pageState == 'view'}">enabled="false"</c:if> />
							</td>
							<td class="form-label">��������</td>
							<td class="form-content">
								<input name="result.identityType" class="mini-combobox" value="${result.identityType}"
									data="[{id:'ѧ��',text:'ѧ��'},{id:'ְ��',text:'ְ��'},{id:'�в�',text:'�в�'}]"
									required="true" requiredErrorText="������������Ϊ��" style="width:150px;"
									<c:if test="${pageState == 'view'}">enabled="false"</c:if> />
							</td>
						</tr>
						<tr>
							<td class="form-label">���ô��ң�</td>
							<td class="form-content" colspan="3">
								<input name="result.department" class="mini-textbox" value="${result.department}" 
									style="width:400px;" <c:if test="${pageState == 'view'}">enabled="false"</c:if> />
							</td>
						</tr>
						<tr>
							<td class="form-label">��ʼ���ڣ�</td>
							<td class="form-content">
								<input id="startDate" name="result.startDate" class="mini-datepicker" 
									value="${result.startDate}" format="yyyy-MM-dd" 
									required="true" requiredErrorText="��ʼ���ڲ���Ϊ��" style="width:150px;"
									onvaluechanged="calculateTravelDays()"
									<c:if test="${pageState == 'view'}">enabled="false"</c:if> />
							</td>
							<td class="form-label">��������</td>
							<td class="form-content">
								<input id="endDate" name="result.endDate" class="mini-datepicker" 
									value="${result.endDate}" format="yyyy-MM-dd" 
									required="true" requiredErrorText="�������ڲ���Ϊ��" style="width:150px;"
									onvaluechanged="calculateTravelDays()"
									<c:if test="${pageState == 'view'}">enabled="false"</c:if> />
							</td>
						</tr>
						<tr>
							<td class="form-label">���������</td>
							<td class="form-content">
								<input id="travelDays" name="result.travelDays" class="mini-spinner" 
									value="${result.travelDays}" style="width:150px;" enabled="false" />
							</td>
							<td class="form-label">����״̬��</td>
							<td class="form-content">
								<input name="result.auditState" class="mini-textbox" value="${result.auditState}" 
									style="width:150px;" enabled="false" />
							</td>
						</tr>
						<tr>
							<td class="form-label">Ŀ�ĵأ�</td>
							<td class="form-content" colspan="3">
								<input name="result.destination" class="mini-textbox" value="${result.destination}" 
									required="true" requiredErrorText="Ŀ�ĵز���Ϊ��" style="width:400px;"
									<c:if test="${pageState == 'view'}">enabled="false"</c:if> />
							</td>
						</tr>
						<tr>
							<td class="form-label">���������</td>
							<td class="form-content" colspan="3">
								<input name="result.travelReason" class="mini-textarea" value="${result.travelReason}" 
									required="true" requiredErrorText="������ɲ���Ϊ��" style="width:400px;height:80px;"
									<c:if test="${pageState == 'view'}">enabled="false"</c:if> />
							</td>
						</tr>
						<c:if test="${pageState == 'view' && !empty result.auditOpinion}">
						<tr>
							<td class="form-label">������������</td>
							<td class="form-content" colspan="3">
								<input name="result.auditOpinion" class="mini-textarea" value="${result.auditOpinion}" 
									style="width:400px;height:60px;" enabled="false" />
							</td>
						</tr>
						</c:if>
					</table>
				</div>
			</form>
		</div>
	</div>
	
	<!-- ������������ -->
	<div id="win1" class="mini-window" title="������������" style="width:400px;height:200px;" 
		showModal="true" allowResize="false" allowDrag="false">
		<div class="mini-fit" style="padding:10px;">
			<div style="margin-bottom:10px;">
				<label>������������</label>
			</div>
			<div>
				<input id="auditOpinion" name="auditOpinion" class="mini-textarea" 
					style="width:100%;height:80px;" required="true" />
			</div>
			<div style="text-align:center;padding-top:10px;">
				<a class="mini-button" onclick="onOk()" style="width:60px;">ȷ��</a>
				<span style="display:inline-block;width:25px;"></span>
				<a class="mini-button" onclick="onCancel()" style="width:60px;">ȡ��</a>
			</div>
		</div>
	</div>
</body>
</html>
