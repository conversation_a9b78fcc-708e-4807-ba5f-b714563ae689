<%@ page language="java" contentType="text/html; charset=GBK"
	pageEncoding="GBK"%>
<%@ page import="ie.weaf.toolkit.Util"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>出京申请 - 维护</title>
<meta http-equiv="content-type" content="text/html; charset=GBK" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<%@ include file="/common/taglibs.jsp"%>
<script type="text/javascript">
	var form;
	$(document).ready(function() {
		mini.parse();
		form = new mini.Form("form1");
		
		// 计算出京天数
		calculateTravelDays();
	});
	
	// 计算出京天数
	function calculateTravelDays() {
		var startDate = mini.get("startDate").getValue();
		var endDate = mini.get("endDate").getValue();
		
		if (startDate && endDate) {
			var start = new Date(startDate);
			var end = new Date(endDate);
			var timeDiff = end.getTime() - start.getTime();
			var dayDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
			
			if (dayDiff > 0) {
				mini.get("travelDays").setValue(dayDiff);
			}
		}
	}
	
	//保存
	function save(e) {
		form.validate();
		if (form.isValid() == false) {
			//显示验证错误信息，400为显示宽度，300为显示高度
			showFormErrorTexts(form.getErrorTexts(), 400, 300);
			form.getErrors()[0].focus();
			return;
		}
		mini.get("saveType").setValue(e);//提交方式
		if ("${pageState}" == "add") {
			document.form1.action = "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_addParentSubmit.ac";
		} else {
			document.form1.action = "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_edtParentSubmit.ac";
		}
		document.form1.submit();
		waitClick();
	}
	
	//审批提交
	function toSubmit(type) {
		mini.get("auditResult").setValue(type);
		if ("1" == type) {
			mini.get("auditOpinion").setValue("同意");
		} else {
			mini.get("auditOpinion").setValue("不同意");
		}
		var win = mini.get("win1");
		//显示弹出window
		win.showAtPos("center", "middle");
	}
	
	//确认提交
	function onOk() {
		var auditOpinion = mini.get("auditOpinion").getValue();
		if (!auditOpinion) {
			mini.alert("请填写审批意见！");
			return;
		}
		
		document.form1.action = "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_auditSubmit.ac";
		document.form1.submit();
		waitClick();
	}
	
	//取消
	function onCancel() {
		var win = mini.get("win1");
		win.hide();
	}
</script>
</head>
<body>
	<div class="mini-fit">
		<div class="mini-toolbar" style="border-bottom: 0; padding: 0px;">
			<table style="width: 100%;">
				<tr>
					<td style="width: 100%;">
						<a class="mini-button" iconCls="icon-save" onclick="save('0')" plain="true">保存</a>
						<a class="mini-button" iconCls="icon-upload" onclick="save('1')" plain="true">提交</a>
						<span class="separator"></span>
						<c:if test="${parama != '0' && pageState == 'view'}">
							<a class="mini-button" iconCls="icon-ok" onclick="toSubmit('1')" plain="true">同意</a>
							<a class="mini-button" iconCls="icon-cancel" onclick="toSubmit('0')" plain="true">驳回</a>
							<span class="separator"></span>
						</c:if>
						<a class="mini-button" iconCls="icon-close" onclick="CloseWindow('reload')" plain="true">关闭</a>
					</td>
				</tr>
			</table>
		</div>
		
		<div class="mini-fit">
			<form id="form1" method="post">
				<input name="id" class="mini-hidden" value="${result.id}" />
				<input name="piId" class="mini-hidden" value="${result.piId}" />
				<input name="taskid" class="mini-hidden" value="${taskid}" />
				<input name="parama" class="mini-hidden" value="${parama}" />
				<input id="saveType" name="saveType" class="mini-hidden" />
				<input id="auditResult" name="auditResult" class="mini-hidden" />
				
				<div class="form-container3">
					<table class="form-table">
						<tr>
							<td class="form-label">申请人姓名：</td>
							<td class="form-content">
								<input name="result.applicantName" class="mini-textbox" value="${result.applicantName}" 
									required="true" requiredErrorText="申请人姓名不能为空" style="width:150px;" 
									<c:if test="${pageState == 'view'}">enabled="false"</c:if> />
							</td>
							<td class="form-label">身份类型：</td>
							<td class="form-content">
								<input name="result.identityType" class="mini-combobox" value="${result.identityType}"
									data="[{id:'学生',text:'学生'},{id:'职工',text:'职工'},{id:'中层',text:'中层'}]"
									required="true" requiredErrorText="身份类型不能为空" style="width:150px;"
									<c:if test="${pageState == 'view'}">enabled="false"</c:if> />
							</td>
						</tr>
						<tr>
							<td class="form-label">所属科室：</td>
							<td class="form-content" colspan="3">
								<input name="result.department" class="mini-textbox" value="${result.department}" 
									style="width:400px;" <c:if test="${pageState == 'view'}">enabled="false"</c:if> />
							</td>
						</tr>
						<tr>
							<td class="form-label">起始日期：</td>
							<td class="form-content">
								<input id="startDate" name="result.startDate" class="mini-datepicker" 
									value="${result.startDate}" format="yyyy-MM-dd" 
									required="true" requiredErrorText="起始日期不能为空" style="width:150px;"
									onvaluechanged="calculateTravelDays()"
									<c:if test="${pageState == 'view'}">enabled="false"</c:if> />
							</td>
							<td class="form-label">结束日期：</td>
							<td class="form-content">
								<input id="endDate" name="result.endDate" class="mini-datepicker" 
									value="${result.endDate}" format="yyyy-MM-dd" 
									required="true" requiredErrorText="结束日期不能为空" style="width:150px;"
									onvaluechanged="calculateTravelDays()"
									<c:if test="${pageState == 'view'}">enabled="false"</c:if> />
							</td>
						</tr>
						<tr>
							<td class="form-label">出京天数：</td>
							<td class="form-content">
								<input id="travelDays" name="result.travelDays" class="mini-spinner" 
									value="${result.travelDays}" style="width:150px;" enabled="false" />
							</td>
							<td class="form-label">审批状态：</td>
							<td class="form-content">
								<input name="result.auditState" class="mini-textbox" value="${result.auditState}" 
									style="width:150px;" enabled="false" />
							</td>
						</tr>
						<tr>
							<td class="form-label">目的地：</td>
							<td class="form-content" colspan="3">
								<input name="result.destination" class="mini-textbox" value="${result.destination}" 
									required="true" requiredErrorText="目的地不能为空" style="width:400px;"
									<c:if test="${pageState == 'view'}">enabled="false"</c:if> />
							</td>
						</tr>
						<tr>
							<td class="form-label">出京事由：</td>
							<td class="form-content" colspan="3">
								<input name="result.travelReason" class="mini-textarea" value="${result.travelReason}" 
									required="true" requiredErrorText="出京事由不能为空" style="width:400px;height:80px;"
									<c:if test="${pageState == 'view'}">enabled="false"</c:if> />
							</td>
						</tr>
						<c:if test="${pageState == 'view' && !empty result.auditOpinion}">
						<tr>
							<td class="form-label">审批意见：</td>
							<td class="form-content" colspan="3">
								<input name="result.auditOpinion" class="mini-textarea" value="${result.auditOpinion}" 
									style="width:400px;height:60px;" enabled="false" />
							</td>
						</tr>
						</c:if>
					</table>
				</div>
			</form>
		</div>
	</div>
	
	<!-- 审批意见窗口 -->
	<div id="win1" class="mini-window" title="审批意见" style="width:400px;height:200px;" 
		showModal="true" allowResize="false" allowDrag="false">
		<div class="mini-fit" style="padding:10px;">
			<div style="margin-bottom:10px;">
				<label>审批意见：</label>
			</div>
			<div>
				<input id="auditOpinion" name="auditOpinion" class="mini-textarea" 
					style="width:100%;height:80px;" required="true" />
			</div>
			<div style="text-align:center;padding-top:10px;">
				<a class="mini-button" onclick="onOk()" style="width:60px;">确定</a>
				<span style="display:inline-block;width:25px;"></span>
				<a class="mini-button" onclick="onCancel()" style="width:60px;">取消</a>
			</div>
		</div>
	</div>
</body>
</html>
