<%@page import="cn.com.sinosoft.os.constant.OsConstant"%>
<%@ page language="java" contentType="text/html; charset=GBK" pageEncoding="GBK"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<jsp:directive.page import="ie.weaf.toolkit.Util" />
<%@ include file="/common/taglibs.jsp"%>
<%
	String title = "出京申请"; // 标题
	String viewFunc = "OS000030101"; // 查看权限
	String addFunc = "OS000030102"; // 新增权限
	String edtFunc = "OS000030103"; // 修改权限
	String delFunc = "OS000030104"; // 删除权限
	String submitFunc = "OS000030106"; // 提交权限
	String revokeFunc = "OS000030107"; //撤销流程权限
	String expFunc = "OS000030108"; // 导出权限
	String kyFucn = "OS0000801";//科员查询权限
	String kzFucn = "OS0000802";//科长查询权限 
	String allFucn = "OS0000803";//管理查询权限
	String isAllFunc = "";
	String isKzFucn = "";
	String isKyFucn = "";
	String opers =  taglibs_userview.getOpers();
	if(!Util.isNullOrEmpty(opers)){
		String arrsOpers[] = opers.split(",");
		for(int i=0;i<arrsOpers.length;i++){
			if(allFucn.equals(arrsOpers[i]) ){
				isAllFunc = "1";
			}
			if(kzFucn.equals(arrsOpers[i])){
				isKzFucn="1";
			}
			if(kyFucn.equals(arrsOpers[i])){
				isKyFucn="1";
			}
		}
	} 
%>
<html>
<head>
	<title><%=title%></title>
	<meta http-equiv="content-type" content="text/html; charset=GBK" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	
	<c:set value="<%=isAllFunc %>" var="isAllFunc"></c:set>
	<c:set value="<%=isKzFucn %>" var="isKzFucn"></c:set>
	<c:set value="<%=isKyFucn %>" var="isKyFucn"></c:set>
	<style type="text/css">
	.myrow {
		 color: gray;
	}
	</style>
	<script type="text/javascript">
		mini.parse();
		var grid = mini.get("datagrid1");
		grid.load();
		
		//查询
		function search() {
			var form = new mini.Form("form1");
			grid.load(form.getData());
		}
		
		//重置
		function onReset() {
			var form = new mini.Form("form1");
			form.reset();
			grid.clearFilter();
		}
		
		//新增
		function add() {
			mini.open({
				url: "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_addParentInput.ac",
				title: "出京申请 - 新增",
				width: 800,
				height: 600,
				onload: function () {
				},
				ondestroy: function (action) {
					if (action == "ok" || action == "reload") {
						grid.reload();
					}
				}
			});
		}
		
		//编辑
		function edit() {
			var row = grid.getSelected();
			if (row) {
				mini.open({
					url: "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_edtParentInput.ac?id=" + row.id,
					title: "出京申请 - 编辑",
					width: 800,
					height: 600,
					onload: function () {
					},
					ondestroy: function (action) {
						if (action == "ok" || action == "reload") {
							grid.reload();
						}
					}
				});
			} else {
				mini.alert("请选择一条记录");
			}
		}
		
		//查看
		function view() {
			var row = grid.getSelected();
			if (row) {
				mini.open({
					url: "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_viewParentInput.ac?id=" + row.id,
					title: "出京申请 - 查看",
					width: 800,
					height: 600,
					onload: function () {
					},
					ondestroy: function (action) {
						if (action == "ok" || action == "reload") {
							grid.reload();
						}
					}
				});
			} else {
				mini.alert("请选择一条记录");
			}
		}
		
		//删除
		function remove() {
			var rows = grid.getSelecteds();
			if (rows.length > 0) {
				mini.confirm("确定删除选中记录？", "确定？",
					function (action) {
						if (action == "ok") {
							var ids = [];
							for (var i = 0, l = rows.length; i < l; i++) {
								var r = rows[i];
								ids.push(r.id);
							}
							var id = ids.join(',');
							grid.loading("操作中，请稍后......");
							$.ajax({
								url: "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_deleteParentSubmit.ac?id=" + id,
								cache: false,
								success: function (text) {
									grid.reload();
								},
								error: function () {
									mini.alert("删除失败");
									grid.unmask();
								}
							});
						}
					}
				);
			} else {
				mini.alert("请选择要删除的记录");
			}
		}
		
		//导出
		function exportData() {
			var form = new mini.Form("form1");
			var params = mini.encode(form.getData());
			window.open("${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_exportParentSubmit.ac?" + params);
		}
		
		//行双击事件
		function onRowDblClick(e) {
			view();
		}
		
		//渲染审批状态
		function onAuditStateRenderer(e) {
			var auditState = e.value;
			if (auditState == "0") {
				return "草稿";
			} else if (auditState == "1") {
				return "审批中";
			} else if (auditState == "2") {
				return "已通过";
			} else if (auditState == "3") {
				return "已驳回";
			} else {
				return auditState;
			}
		}
		
		//渲染身份类型
		function onIdentityTypeRenderer(e) {
			var identityType = e.value;
			if (identityType == "学生") {
				return "学生";
			} else if (identityType == "职工") {
				return "职工";
			} else if (identityType == "中层") {
				return "中层";
			} else {
				return identityType;
			}
		}
	</script>
</head>
<body>
	<div class="mini-fit">
		<div class="mini-toolbar" style="border-bottom:0;padding:0px;">
			<table style="width:100%;">
				<tr>
					<td style="width:100%;">
						<a class="mini-button" iconCls="icon-search" onclick="search()" plain="true">查询</a>
						<a class="mini-button" iconCls="icon-reset" onclick="onReset()" plain="true">重置</a>
						<span class="separator"></span>
						<a class="mini-button" iconCls="icon-add" onclick="add()" plain="true">新增</a>
						<a class="mini-button" iconCls="icon-edit" onclick="edit()" plain="true">编辑</a>
						<a class="mini-button" iconCls="icon-view" onclick="view()" plain="true">查看</a>
						<a class="mini-button" iconCls="icon-remove" onclick="remove()" plain="true">删除</a>
						<span class="separator"></span>
						<a class="mini-button" iconCls="icon-download" onclick="exportData()" plain="true">导出</a>
					</td>
				</tr>
			</table>
		</div>
		
		<div class="mini-fit" style="padding:5px;">
			<form id="form1">
				<table class="condition-table">
					<tr>
						<td class="condition-label">申请人姓名：</td>
						<td class="condition-content">
							<input name="applicantName" class="mini-textbox" style="width:120px;" />
						</td>
						<td class="condition-label">身份类型：</td>
						<td class="condition-content">
							<input name="identityType" class="mini-combobox" style="width:120px;"
								data="[{id:'',text:'全部'},{id:'学生',text:'学生'},{id:'职工',text:'职工'},{id:'中层',text:'中层'}]" />
						</td>
						<td class="condition-label">审批状态：</td>
						<td class="condition-content">
							<input name="auditState" class="mini-combobox" style="width:120px;"
								data="[{id:'',text:'全部'},{id:'0',text:'草稿'},{id:'1',text:'审批中'},{id:'2',text:'已通过'},{id:'3',text:'已驳回'}]" />
						</td>
					</tr>
					<tr>
						<td class="condition-label">申请日期：</td>
						<td class="condition-content">
							<input name="applyDateStart" class="mini-datepicker" style="width:120px;" format="yyyy-MM-dd" />
						</td>
						<td class="condition-label">至：</td>
						<td class="condition-content">
							<input name="applyDateEnd" class="mini-datepicker" style="width:120px;" format="yyyy-MM-dd" />
						</td>
						<td class="condition-label">目的地：</td>
						<td class="condition-content">
							<input name="destination" class="mini-textbox" style="width:120px;" />
						</td>
					</tr>
				</table>
			</form>
			
			<div class="mini-fit" style="padding-top:8px;">
				<div id="datagrid1" class="mini-datagrid" style="width:100%;height:100%;" 
					url="${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_qryParentSubmit.ac"
					idField="id" multiSelect="true" showPager="true" pageSize="20"
					onrowdblclick="onRowDblClick">
					<div property="columns">
						<div type="checkcolumn" width="20"></div>
						<div field="applicantName" width="100" headerAlign="center" allowSort="true">申请人姓名</div>
						<div field="identityType" width="80" headerAlign="center" allowSort="true" renderer="onIdentityTypeRenderer">身份类型</div>
						<div field="department" width="150" headerAlign="center" allowSort="true">所属科室</div>
						<div field="destination" width="150" headerAlign="center" allowSort="true">目的地</div>
						<div field="startDate" width="100" headerAlign="center" allowSort="true" dateFormat="yyyy-MM-dd">起始日期</div>
						<div field="endDate" width="100" headerAlign="center" allowSort="true" dateFormat="yyyy-MM-dd">结束日期</div>
						<div field="travelDays" width="80" headerAlign="center" allowSort="true">出京天数</div>
						<div field="auditState" width="80" headerAlign="center" allowSort="true" renderer="onAuditStateRenderer">审批状态</div>
						<div field="applyDate" width="100" headerAlign="center" allowSort="true" dateFormat="yyyy-MM-dd">申请日期</div>
						<div field="addUser" width="100" headerAlign="center" allowSort="true">申请人</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</body>
</html>
