<%@page import="cn.com.sinosoft.os.constant.OsConstant"%>
<%@ page language="java" contentType="text/html; charset=GBK" pageEncoding="GBK"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<jsp:directive.page import="ie.weaf.toolkit.Util" />
<%@ include file="/common/taglibs.jsp"%>
<%
	String title = "�����������"; // ����
	String viewFunc = "OS000030101"; // �鿴Ȩ��
	String addFunc = "OS000030102"; // ����Ȩ��
	String edtFunc = "OS000030103"; // �޸�Ȩ��
	String delFunc = "OS000030104"; // ɾ��Ȩ��
	String submitFunc = "OS000030106"; // �ύȨ��
	String revokeFunc = "OS000030107"; //����������Ȩ��
	String expFunc = "OS000030108"; // ����Ȩ��
	String kyFucn = "OS0000801";//��Ա��ѯȨ��
	String kzFucn = "OS0000802";//����ѯȨ�� 
	String allFucn = "OS0000803";//������ѯȨ��
	String isAllFunc = "";
	String isKzFucn = "";
	String isKyFucn = "";
	if(Util.hasFunc(request, allFucn)){
		isAllFunc = "true";
	}
	if(Util.hasFunc(request, kzFucn)){
		isKzFucn = "true";
	}
	if(Util.hasFunc(request, kyFucn)){
		isKyFucn = "true";
	}
%>
<html>
<head>
<title><%=title%></title>
<meta http-equiv="content-type" content="text/html; charset=GBK" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<script type="text/javascript">
	var grid;
	$(document).ready(function() {
		mini.parse();
		grid = mini.get("datagrid1");
		grid.load();
	});

	//��ѯ
	function search() {
		var form = new mini.Form("form1");
		grid.load(form.getData());
	}

	//����
	function reset() {
		var form = new mini.Form("form1");
		form.clear();
		grid.clearFilter();
	}

	//����
	function add() {
		mini.open({
			url : "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_addParentInput.ac",
			title : "����" + "<%=title%>",
			width : 800,
			height : 600,
			onload : function() {
			},
			ondestroy : function(action) {
				if (action == "ok" || action == "reload") {
					grid.reload();
				}
			}
		});
	}

	//�鿴
	function view() {
		var row = grid.getSelected();
		if (row) {
			mini.open({
				url : "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_viewParentSubmit.ac?id=" + row.id,
				title : "�鿴" + "<%=title%>",
				width : 800,
				height : 600,
				onload : function() {
				},
				ondestroy : function(action) {
					if (action == "ok" || action == "reload") {
						grid.reload();
					}
				}
			});
		} else {
			mini.alert("��ѡ��һ����¼");
		}
	}

	//�޸�
	function edit() {
		var row = grid.getSelected();
		if (row) {
			mini.open({
				url : "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_editParentInput.ac?id=" + row.id,
				title : "�޸�" + "<%=title%>",
				width : 800,
				height : 600,
				onload : function() {
				},
				ondestroy : function(action) {
					if (action == "ok" || action == "reload") {
						grid.reload();
					}
				}
			});
		} else {
			mini.alert("��ѡ��һ����¼");
		}
	}

	//ɾ��
	function remove() {
		var rows = grid.getSelecteds();
		if (rows.length > 0) {
			mini.confirm("ȷ��ɾ����", "ȷ��",
				function(action) {
					if (action == "ok") {
						var ids = [];
						for (var i = 0, l = rows.length; i < l; i++) {
							var r = rows[i];
							ids.push(r.id);
						}
						var id = ids.join(',');
						grid.loading("�����ύ�У�����Ժ�...");
						$.ajax({
							url : "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_delteParentSubmit.ac",
							data : {
								id : id
							},
							type : "post",
							success : function(text) {
								grid.reload();
							},
							error : function() {
							}
						});
					}
				});
		} else {
			mini.alert("��ѡ��һ����¼");
		}
	}

	//�ύ
	function submitApply() {
		var rows = grid.getSelecteds();
		if (rows.length > 0) {
			mini.confirm("ȷ���ύ��", "ȷ��",
				function(action) {
					if (action == "ok") {
						var ids = [];
						for (var i = 0, l = rows.length; i < l; i++) {
							var r = rows[i];
							ids.push(r.id);
						}
						var id = ids.join(',');
						grid.loading("�����ύ�У�����Ժ�...");
						$.ajax({
							url : "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_submitApply.ac",
							data : {
								id : id
							},
							type : "post",
							success : function(text) {
								grid.reload();
							},
							error : function() {
							}
						});
					}
				});
		} else {
			mini.alert("��ѡ��һ����¼");
		}
	}

	//����
	function revokeApply() {
		var rows = grid.getSelecteds();
		if (rows.length > 0) {
			mini.confirm("ȷ�����ѣ�", "ȷ��",
				function(action) {
					if (action == "ok") {
						var ids = [];
						for (var i = 0, l = rows.length; i < l; i++) {
							var r = rows[i];
							ids.push(r.id);
						}
						var id = ids.join(',');
						grid.loading("�����ύ�У�����Ժ�...");
						$.ajax({
							url : "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_revokeApply.ac",
							data : {
								id : id
							},
							type : "post",
							success : function(text) {
								grid.reload();
							},
							error : function() {
							}
						});
					}
				});
		} else {
			mini.alert("��ѡ��һ����¼");
		}
	}

	//����
	function exportData() {
		var form = new mini.Form("form1");
		var data = form.getData();
		var params = mini.encode(data);
		window.open("${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_export.ac?" + params);
	}

	//��Ⱦ����״̬
	function onAuditStateRenderer(e) {
		var record = e.record;
		var value = e.value;
		if (value == "<%=OsConstant.AUDIT_STATE_ONE%>") {
			return "�ݸ�";
		} else if (value == "<%=OsConstant.AUDIT_STATE_TWO%>") {
			return "�����";
		} else if (value == "<%=OsConstant.AUDIT_STATE_THREE%>") {
			return "������";
		} else {
			return value;
		}
	}
</script>
</head>
<body>
	<div class="mini-fit">
		<div class="mini-toolbar" style="border-bottom: 0; padding: 0px;">
			<table style="width: 100%;">
				<tr>
					<td style="width: 100%;">
						<% if(Util.hasFunc(request, addFunc)){ %>
						<a class="mini-button" iconCls="icon-add" onclick="add()" plain="true">����</a>
						<% } %>
						<% if(Util.hasFunc(request, viewFunc)){ %>
						<a class="mini-button" iconCls="icon-view" onclick="view()" plain="true">�鿴</a>
						<% } %>
						<% if(Util.hasFunc(request, edtFunc)){ %>
						<a class="mini-button" iconCls="icon-edit" onclick="edit()" plain="true">�޸�</a>
						<% } %>
						<% if(Util.hasFunc(request, delFunc)){ %>
						<a class="mini-button" iconCls="icon-remove" onclick="remove()" plain="true">ɾ��</a>
						<% } %>
						<span class="separator"></span>
						<% if(Util.hasFunc(request, submitFunc)){ %>
						<a class="mini-button" iconCls="icon-upload" onclick="submitApply()" plain="true">�ύ</a>
						<% } %>
						<% if(Util.hasFunc(request, revokeFunc)){ %>
						<a class="mini-button" iconCls="icon-undo" onclick="revokeApply()" plain="true">����</a>
						<% } %>
						<span class="separator"></span>
						<% if(Util.hasFunc(request, expFunc)){ %>
						<a class="mini-button" iconCls="icon-download" onclick="exportData()" plain="true">����</a>
						<% } %>
					</td>
				</tr>
			</table>
		</div>
		
		<div class="mini-fit">
			<div class="mini-splitter" style="width:100%;height:100%;" split="horizontal">
				<div size="80" showCollapseButton="false">
					<div class="mini-toolbar">
						<form id="form1">
							<table>
								<tr>
									<td>����������</td>
									<td>
										<input name="applicantName" class="mini-textbox" style="width:120px;" />
									</td>
									<td>��������</td>
									<td>
										<input name="identityType" class="mini-combobox" style="width:120px;"
											data="[{id:'',text:'ȫ��'},{id:'ѧ��',text:'ѧ��'},{id:'ְ��',text:'ְ��'},{id:'�в�',text:'�в�'}]" />
									</td>
									<td>����״̬��</td>
									<td>
										<input name="auditState" class="mini-combobox" style="width:120px;"
											data="[{id:'',text:'ȫ��'},{id:'<%=OsConstant.AUDIT_STATE_ONE%>',text:'�ݸ�'},{id:'<%=OsConstant.AUDIT_STATE_TWO%>',text:'�����'},{id:'<%=OsConstant.AUDIT_STATE_THREE%>',text:'������'}]" />
									</td>
									<td>
										<a class="mini-button" onclick="search()" iconCls="icon-search">��ѯ</a>
										<a class="mini-button" onclick="reset()" iconCls="icon-reset">����</a>
									</td>
								</tr>
							</table>
						</form>
					</div>
				</div>
				<div showCollapseButton="false">
					<div id="datagrid1" class="mini-datagrid" style="width:100%;height:100%;" 
						url="${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_qryParentInput.ac"
						idField="id" multiSelect="true" showPager="true" pageSize="20" allowSortColumn="false">
						<div property="columns">
							<div type="checkcolumn" width="20"></div>
							<div field="applicantName" width="100" headerAlign="center" allowSort="true">����������</div>
							<div field="identityType" width="80" headerAlign="center" allowSort="true">��������</div>
							<div field="department" width="150" headerAlign="center" allowSort="true">���ô��ң�</div>
							<div field="startDate" width="100" headerAlign="center" allowSort="true" dateFormat="yyyy-MM-dd">��ʼ����</div>
							<div field="endDate" width="100" headerAlign="center" allowSort="true" dateFormat="yyyy-MM-dd">��������</div>
							<div field="destination" width="150" headerAlign="center" allowSort="true">Ŀ�ĵ�</div>
							<div field="auditState" width="80" headerAlign="center" allowSort="true" renderer="onAuditStateRenderer">����״̬</div>
							<div field="createTime" width="120" headerAlign="center" allowSort="true" dateFormat="yyyy-MM-dd HH:mm">��������</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</body>
</html>
