<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.activiti.org/test">
  <process id="rsPersonalOut" name="因私出国（境）申请" isExecutable="true">
    <startEvent id="startevent1" name="Start"></startEvent>
    <userTask id="usertask1" name="第一步：科室负责人意见" activiti:candidateGroups="RS0000901">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/rs/personalout/personalOut_auditParentInput.ac" default="1&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <sequenceFlow id="flow1" sourceRef="startevent1" targetRef="usertask1"></sequenceFlow>
    <userTask id="usertask2" name="第二步：科教外事处审批" activiti:assignee="${user21}">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/rs/personalout/personalOut_auditParentInput.ac" default="21&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <sequenceFlow id="flow3" sourceRef="inclusivegateway1" targetRef="usertask2"></sequenceFlow>
    <userTask id="usertask3" name="第二步：所办公室审批" activiti:assignee="${user22}">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/rs/personalout/personalOut_auditParentInput.ac" default="22&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <sequenceFlow id="flow4" sourceRef="inclusivegateway1" targetRef="usertask3"></sequenceFlow>
    <exclusiveGateway id="exclusivegateway1" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow5" sourceRef="usertask1" targetRef="exclusivegateway1"></sequenceFlow>
    <sequenceFlow id="flow6" sourceRef="exclusivegateway1" targetRef="inclusivegateway1">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1'}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="usertask4" name="申请人订正" activiti:assignee="${startUser}">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/rs/personalout/personalOut_auditParentInput.ac" default="0&amp;pageState=edit"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <sequenceFlow id="flow7" name="驳回" sourceRef="exclusivegateway1" targetRef="usertask4">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag!='1'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow8" sourceRef="usertask4" targetRef="usertask1"></sequenceFlow>
    <sequenceFlow id="flow9" sourceRef="usertask2" targetRef="inclusivegateway2"></sequenceFlow>
    <sequenceFlow id="flow10" sourceRef="usertask3" targetRef="inclusivegateway2"></sequenceFlow>
    <sequenceFlow id="flow11" name="其中一个或全部驳回" sourceRef="exclusivegateway7" targetRef="usertask4">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag!='1' and  (step21=='0' or step22=='0')}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="usertask5" name="第三步：主管所领导签字" activiti:assignee="${user3}">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/rs/personalout/personalOut_auditParentInput.ac" default="3&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="exclusivegateway3" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow13" sourceRef="usertask5" targetRef="exclusivegateway3"></sequenceFlow>
    <userTask id="usertask6" name="第四步：所长签字" activiti:assignee="${user4}">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/rs/personalout/personalOut_auditParentInput.ac" default="4&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <sequenceFlow id="flow14" sourceRef="exclusivegateway3" targetRef="usertask6">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1' and step3=='1'}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="usertask7" name="第五步：党委书记签字" activiti:assignee="${user5}">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/rs/personalout/personalOut_auditParentInput.ac" default="5&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <sequenceFlow id="flow15" sourceRef="exclusivegateway3" targetRef="usertask7">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1' and step3=='2'}]]></conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="exclusivegateway4" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow16" sourceRef="usertask6" targetRef="exclusivegateway4"></sequenceFlow>
    <sequenceFlow id="flow17" sourceRef="exclusivegateway4" targetRef="usertask7">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1' and step4=='1'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow18" sourceRef="exclusivegateway3" targetRef="inclusivegateway3">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1' and step3=='3'}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="usertask8" name="第六步：人事处备案" activiti:assignee="${user61}">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/rs/personalout/personalOut_auditParentInput.ac" default="61&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <sequenceFlow id="flow19" sourceRef="inclusivegateway3" targetRef="usertask8"></sequenceFlow>
    <userTask id="usertask9" name="第六步：保密办谈话" activiti:assignee="${user62}">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/rs/personalout/personalOut_auditParentInput.ac" default="62&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <sequenceFlow id="flow20" sourceRef="inclusivegateway3" targetRef="usertask9"></sequenceFlow>
    <sequenceFlow id="flow21" sourceRef="usertask8" targetRef="inclusivegateway4"></sequenceFlow>
    <sequenceFlow id="flow22" sourceRef="usertask9" targetRef="inclusivegateway4"></sequenceFlow>
    <endEvent id="endevent1" name="End"></endEvent>
    <sequenceFlow id="flow23" sourceRef="inclusivegateway4" targetRef="endevent1"></sequenceFlow>
    <exclusiveGateway id="exclusivegateway6" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow24" sourceRef="usertask7" targetRef="exclusivegateway6"></sequenceFlow>
    <sequenceFlow id="flow25" sourceRef="exclusivegateway6" targetRef="inclusivegateway3">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1' }]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow27" name="驳回" sourceRef="exclusivegateway3" targetRef="usertask4">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag!='1'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow28" name="驳回" sourceRef="exclusivegateway4" targetRef="usertask4">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag!='1'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow29" sourceRef="exclusivegateway6" targetRef="usertask4">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag!='1'}]]></conditionExpression>
    </sequenceFlow>
    <inclusiveGateway id="inclusivegateway1" name="Exclusive Gateway"></inclusiveGateway>
    <inclusiveGateway id="inclusivegateway2" name="Exclusive Gateway"></inclusiveGateway>
    <exclusiveGateway id="exclusivegateway7" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow30" sourceRef="inclusivegateway2" targetRef="exclusivegateway7"></sequenceFlow>
    <sequenceFlow id="flow31" name="全部通过" sourceRef="exclusivegateway7" targetRef="usertask5">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1' and step21=='1' and step22=='1'}]]></conditionExpression>
    </sequenceFlow>
    <inclusiveGateway id="inclusivegateway3" name="Exclusive Gateway"></inclusiveGateway>
    <inclusiveGateway id="inclusivegateway4" name="Exclusive Gateway"></inclusiveGateway>
    <sequenceFlow id="flow32" sourceRef="exclusivegateway4" targetRef="inclusivegateway3">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1' and step4=='2'}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_rsPersonalOut">
    <bpmndi:BPMNPlane bpmnElement="rsPersonalOut" id="BPMNPlane_rsPersonalOut">
      <bpmndi:BPMNShape bpmnElement="startevent1" id="BPMNShape_startevent1">
        <omgdc:Bounds height="35.0" width="35.0" x="168.0" y="23.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask1" id="BPMNShape_usertask1">
        <omgdc:Bounds height="71.0" width="131.0" x="120.0" y="136.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask2" id="BPMNShape_usertask2">
        <omgdc:Bounds height="64.0" width="136.0" x="580.0" y="45.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask3" id="BPMNShape_usertask3">
        <omgdc:Bounds height="71.0" width="136.0" x="580.0" y="136.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway1" id="BPMNShape_exclusivegateway1">
        <omgdc:Bounds height="40.0" width="40.0" x="350.0" y="151.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask4" id="BPMNShape_usertask4">
        <omgdc:Bounds height="55.0" width="105.0" x="133.0" y="284.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask5" id="BPMNShape_usertask5">
        <omgdc:Bounds height="67.0" width="125.0" x="938.0" y="43.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway3" id="BPMNShape_exclusivegateway3">
        <omgdc:Bounds height="40.0" width="40.0" x="902.0" y="188.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask6" id="BPMNShape_usertask6">
        <omgdc:Bounds height="73.0" width="116.0" x="724.0" y="270.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask7" id="BPMNShape_usertask7">
        <omgdc:Bounds height="73.0" width="115.0" x="865.0" y="270.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway4" id="BPMNShape_exclusivegateway4">
        <omgdc:Bounds height="40.0" width="40.0" x="761.0" y="376.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask8" id="BPMNShape_usertask8">
        <omgdc:Bounds height="68.0" width="120.0" x="660.0" y="433.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask9" id="BPMNShape_usertask9">
        <omgdc:Bounds height="67.0" width="120.0" x="660.0" y="527.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="endevent1" id="BPMNShape_endevent1">
        <omgdc:Bounds height="35.0" width="35.0" x="420.0" y="490.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway6" id="BPMNShape_exclusivegateway6">
        <omgdc:Bounds height="40.0" width="40.0" x="902.0" y="376.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="inclusivegateway1" id="BPMNShape_inclusivegateway1">
        <omgdc:Bounds height="40.0" width="40.0" x="470.0" y="151.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="inclusivegateway2" id="BPMNShape_inclusivegateway2">
        <omgdc:Bounds height="40.0" width="40.0" x="761.0" y="57.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway7" id="BPMNShape_exclusivegateway7">
        <omgdc:Bounds height="40.0" width="40.0" x="846.0" y="58.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="inclusivegateway3" id="BPMNShape_inclusivegateway3">
        <omgdc:Bounds height="40.0" width="40.0" x="902.0" y="446.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="inclusivegateway4" id="BPMNShape_inclusivegateway4">
        <omgdc:Bounds height="40.0" width="40.0" x="520.0" y="487.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="flow1" id="BPMNEdge_flow1">
        <omgdi:waypoint x="185.0" y="58.0"></omgdi:waypoint>
        <omgdi:waypoint x="185.0" y="136.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow3" id="BPMNEdge_flow3">
        <omgdi:waypoint x="490.0" y="151.0"></omgdi:waypoint>
        <omgdi:waypoint x="490.0" y="76.0"></omgdi:waypoint>
        <omgdi:waypoint x="580.0" y="77.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow4" id="BPMNEdge_flow4">
        <omgdi:waypoint x="510.0" y="171.0"></omgdi:waypoint>
        <omgdi:waypoint x="580.0" y="171.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow5" id="BPMNEdge_flow5">
        <omgdi:waypoint x="251.0" y="171.0"></omgdi:waypoint>
        <omgdi:waypoint x="350.0" y="171.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow6" id="BPMNEdge_flow6">
        <omgdi:waypoint x="390.0" y="171.0"></omgdi:waypoint>
        <omgdi:waypoint x="470.0" y="171.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow7" id="BPMNEdge_flow7">
        <omgdi:waypoint x="370.0" y="191.0"></omgdi:waypoint>
        <omgdi:waypoint x="369.0" y="313.0"></omgdi:waypoint>
        <omgdi:waypoint x="238.0" y="311.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="16.0" width="100.0" x="341.0" y="239.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow8" id="BPMNEdge_flow8">
        <omgdi:waypoint x="185.0" y="284.0"></omgdi:waypoint>
        <omgdi:waypoint x="185.0" y="207.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow9" id="BPMNEdge_flow9">
        <omgdi:waypoint x="716.0" y="77.0"></omgdi:waypoint>
        <omgdi:waypoint x="761.0" y="77.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow10" id="BPMNEdge_flow10">
        <omgdi:waypoint x="716.0" y="171.0"></omgdi:waypoint>
        <omgdi:waypoint x="780.0" y="170.0"></omgdi:waypoint>
        <omgdi:waypoint x="781.0" y="97.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow11" id="BPMNEdge_flow11">
        <omgdi:waypoint x="866.0" y="98.0"></omgdi:waypoint>
        <omgdi:waypoint x="780.0" y="209.0"></omgdi:waypoint>
        <omgdi:waypoint x="614.0" y="311.0"></omgdi:waypoint>
        <omgdi:waypoint x="238.0" y="311.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="48.0" width="100.0" x="804.0" y="127.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow13" id="BPMNEdge_flow13">
        <omgdi:waypoint x="1000.0" y="110.0"></omgdi:waypoint>
        <omgdi:waypoint x="922.0" y="188.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow14" id="BPMNEdge_flow14">
        <omgdi:waypoint x="902.0" y="208.0"></omgdi:waypoint>
        <omgdi:waypoint x="782.0" y="208.0"></omgdi:waypoint>
        <omgdi:waypoint x="782.0" y="270.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow15" id="BPMNEdge_flow15">
        <omgdi:waypoint x="922.0" y="228.0"></omgdi:waypoint>
        <omgdi:waypoint x="922.0" y="270.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow16" id="BPMNEdge_flow16">
        <omgdi:waypoint x="782.0" y="343.0"></omgdi:waypoint>
        <omgdi:waypoint x="781.0" y="376.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow17" id="BPMNEdge_flow17">
        <omgdi:waypoint x="781.0" y="376.0"></omgdi:waypoint>
        <omgdi:waypoint x="922.0" y="343.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow18" id="BPMNEdge_flow18">
        <omgdi:waypoint x="942.0" y="208.0"></omgdi:waypoint>
        <omgdi:waypoint x="1054.0" y="208.0"></omgdi:waypoint>
        <omgdi:waypoint x="1054.0" y="465.0"></omgdi:waypoint>
        <omgdi:waypoint x="942.0" y="466.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow19" id="BPMNEdge_flow19">
        <omgdi:waypoint x="902.0" y="466.0"></omgdi:waypoint>
        <omgdi:waypoint x="780.0" y="467.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow20" id="BPMNEdge_flow20">
        <omgdi:waypoint x="922.0" y="486.0"></omgdi:waypoint>
        <omgdi:waypoint x="922.0" y="556.0"></omgdi:waypoint>
        <omgdi:waypoint x="780.0" y="560.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow21" id="BPMNEdge_flow21">
        <omgdi:waypoint x="660.0" y="467.0"></omgdi:waypoint>
        <omgdi:waypoint x="539.0" y="467.0"></omgdi:waypoint>
        <omgdi:waypoint x="540.0" y="487.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow22" id="BPMNEdge_flow22">
        <omgdi:waypoint x="660.0" y="560.0"></omgdi:waypoint>
        <omgdi:waypoint x="540.0" y="560.0"></omgdi:waypoint>
        <omgdi:waypoint x="540.0" y="527.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow23" id="BPMNEdge_flow23">
        <omgdi:waypoint x="520.0" y="507.0"></omgdi:waypoint>
        <omgdi:waypoint x="455.0" y="507.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow24" id="BPMNEdge_flow24">
        <omgdi:waypoint x="922.0" y="343.0"></omgdi:waypoint>
        <omgdi:waypoint x="922.0" y="376.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow25" id="BPMNEdge_flow25">
        <omgdi:waypoint x="922.0" y="416.0"></omgdi:waypoint>
        <omgdi:waypoint x="922.0" y="446.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow27" id="BPMNEdge_flow27">
        <omgdi:waypoint x="902.0" y="208.0"></omgdi:waypoint>
        <omgdi:waypoint x="788.0" y="208.0"></omgdi:waypoint>
        <omgdi:waypoint x="619.0" y="311.0"></omgdi:waypoint>
        <omgdi:waypoint x="238.0" y="311.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="16.0" width="100.0" x="511.0" y="320.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow28" id="BPMNEdge_flow28">
        <omgdi:waypoint x="761.0" y="396.0"></omgdi:waypoint>
        <omgdi:waypoint x="185.0" y="396.0"></omgdi:waypoint>
        <omgdi:waypoint x="185.0" y="339.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="16.0" width="100.0" x="541.0" y="376.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow29" id="BPMNEdge_flow29">
        <omgdi:waypoint x="922.0" y="416.0"></omgdi:waypoint>
        <omgdi:waypoint x="856.0" y="423.0"></omgdi:waypoint>
        <omgdi:waypoint x="185.0" y="423.0"></omgdi:waypoint>
        <omgdi:waypoint x="185.0" y="339.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow30" id="BPMNEdge_flow30">
        <omgdi:waypoint x="801.0" y="77.0"></omgdi:waypoint>
        <omgdi:waypoint x="846.0" y="78.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow31" id="BPMNEdge_flow31">
        <omgdi:waypoint x="886.0" y="78.0"></omgdi:waypoint>
        <omgdi:waypoint x="938.0" y="76.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="16.0" width="100.0" x="872.0" y="94.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow32" id="BPMNEdge_flow32">
        <omgdi:waypoint x="781.0" y="416.0"></omgdi:waypoint>
        <omgdi:waypoint x="922.0" y="446.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>