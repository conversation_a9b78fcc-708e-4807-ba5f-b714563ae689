-- 出京申请表
-- Oracle 数据库表创建语句

CREATE TABLE OS_TRAVEL_APPLY (
    -- 主键ID
    ID VARCHAR2(32) NOT NULL,
    
    -- 基本申请信息
    APPLICANT_NAME VARCHAR2(100) NOT NULL,           -- 姓名（手动填写）
    IDENTITY_TYPE VARCHAR2(20) NOT NULL,             -- 身份（学生、职工）
    DEPARTMENT VARCHAR2(100),                        -- 科室（自动获取）
    APPLY_DATE DATE DEFAULT SYSDATE,                 -- 申请日期
    START_DATE DATE NOT NULL,                        -- 起始日期
    END_DATE DATE NOT NULL,                          -- 结束日期
    TRAVEL_DAYS NUMBER(3),                           -- 天数
    DESTINATION VARCHAR2(200) NOT NULL,              -- 目的地
    TRAVEL_REASON VARCHAR2(1000) NOT NULL,           -- 事由
    
    -- 工作流相关字段
    PI_ID VARCHAR2(64),                              -- 流程实例ID
    AUDIT_STATE VARCHAR2(20) DEFAULT '0',           -- 业务状态（0-草稿，1-审批中，2-已通过，3-已驳回）
    
    -- 审批相关字段
    DEPT_LEADER VARCHAR2(100),                       -- 科室主任/所领导（根据身份和职位确定）
    AUDIT_OPINION VARCHAR2(1000),                    -- 审批意见
    
    -- 系统字段（参考新公章申请）
    ADD_ZONE VARCHAR2(6),                            -- 添加地区
    ADD_ORG VARCHAR2(6),                             -- 添加机构
    ADD_DEP VARCHAR2(6),                             -- 添加科室
    ADD_USER VARCHAR2(30),                           -- 添加人
    ADD_TIME DATE DEFAULT SYSDATE,                   -- 添加时间
    MODY_ZONE VARCHAR2(6),                           -- 修改地区
    MODY_ORG VARCHAR2(6),                            -- 修改机构
    MODY_DEP VARCHAR2(6),                            -- 修改科室
    MODY_USER VARCHAR2(30),                          -- 修改人
    MODY_TIME DATE,                                  -- 修改时间
    STATE VARCHAR2(1) DEFAULT '1',                   -- 是否有效（1-有效，0-无效）
    DATA_SOURCE VARCHAR2(100),                       -- 数据来源
    DATA_MODY_TIME DATE,                             -- 数据修改时间
    
    -- 扩展字段（预留）
    EXTEND_FIELD1 VARCHAR2(200),                     -- 扩展字段1
    EXTEND_FIELD2 VARCHAR2(200),                     -- 扩展字段2
    EXTEND_FIELD3 VARCHAR2(200),                     -- 扩展字段3
    
    -- 约束
    CONSTRAINT PK_OS_TRAVEL_APPLY PRIMARY KEY (ID),
    CONSTRAINT CK_IDENTITY_TYPE CHECK (IDENTITY_TYPE IN ('学生', '职工')),
    CONSTRAINT CK_AUDIT_STATE CHECK (AUDIT_STATE IN ('0', '1', '2', '3')),
    CONSTRAINT CK_STATE CHECK (STATE IN ('0', '1')),
    CONSTRAINT CK_DATE_RANGE CHECK (END_DATE >= START_DATE)
);

-- 创建索引
CREATE INDEX IDX_TRAVEL_APPLY_PI_ID ON OS_TRAVEL_APPLY(PI_ID);
CREATE INDEX IDX_TRAVEL_APPLY_ADD_USER ON OS_TRAVEL_APPLY(ADD_USER);
CREATE INDEX IDX_TRAVEL_APPLY_AUDIT_STATE ON OS_TRAVEL_APPLY(AUDIT_STATE);
CREATE INDEX IDX_TRAVEL_APPLY_ADD_TIME ON OS_TRAVEL_APPLY(ADD_TIME);

-- 添加注释
COMMENT ON TABLE OS_TRAVEL_APPLY IS '出京申请表';
COMMENT ON COLUMN OS_TRAVEL_APPLY.ID IS '主键ID';
COMMENT ON COLUMN OS_TRAVEL_APPLY.APPLICANT_NAME IS '申请人姓名';
COMMENT ON COLUMN OS_TRAVEL_APPLY.IDENTITY_TYPE IS '身份类型：学生、职工';
COMMENT ON COLUMN OS_TRAVEL_APPLY.DEPARTMENT IS '所属科室';
COMMENT ON COLUMN OS_TRAVEL_APPLY.APPLY_DATE IS '申请日期';
COMMENT ON COLUMN OS_TRAVEL_APPLY.START_DATE IS '出京开始日期';
COMMENT ON COLUMN OS_TRAVEL_APPLY.END_DATE IS '出京结束日期';
COMMENT ON COLUMN OS_TRAVEL_APPLY.TRAVEL_DAYS IS '出京天数';
COMMENT ON COLUMN OS_TRAVEL_APPLY.DESTINATION IS '目的地';
COMMENT ON COLUMN OS_TRAVEL_APPLY.TRAVEL_REASON IS '出京事由';
COMMENT ON COLUMN OS_TRAVEL_APPLY.PI_ID IS '工作流程实例ID';
COMMENT ON COLUMN OS_TRAVEL_APPLY.AUDIT_STATE IS '审批状态：0-草稿，1-审批中，2-已通过，3-已驳回';
COMMENT ON COLUMN OS_TRAVEL_APPLY.DEPT_LEADER IS '审批人（科室主任或所领导）';
COMMENT ON COLUMN OS_TRAVEL_APPLY.AUDIT_OPINION IS '审批意见';
