<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="cn.com.sinosoft.rs.personalout.model.PersonalOut" table="RS_PERSONAL_OUT">
		<id name="id" column="ID" type="java.lang.String">
			<generator class="uuid"></generator>
		</id>
		<property name="name" column="NAME" type="java.lang.String"/>
		<property name="gender" column="GENDER" type="java.lang.String"/>
		<property name="political" column="POLITICAL" type="java.lang.String"/>
		<property name="birthdate" column="BIRTHDATE" type="java.util.Date"/>
		<property name="outDate" column="OUT_DATE" type="java.util.Date"/>
		<property name="goNation" column="GO_NATION" type="java.lang.String"/>
		<property name="backDate" column="BACK_DATE" type="java.util.Date"/>
		<property name="applyRemark" column="APPLY_REMARK" type="java.lang.String"/>
		<property name="piId" column="PI_ID" type="java.lang.String"/>
		<property name="proState" column="PRO_STATE" type="java.lang.String"/>
		<property name="addZone" column="ADD_ZONE" type="java.lang.String"/>
		<property name="addOrg" column="ADD_ORG" type="java.lang.String"/>
		<property name="addDep" column="ADD_DEP" type="java.lang.String"/>
		<property name="addUser" column="ADD_USER" type="java.lang.String"/>
		<property name="addTime" column="ADD_TIME" type="java.util.Date"/>
		<property name="modyZone" column="MODY_ZONE" type="java.lang.String"/>
		<property name="modyOrg" column="MODY_ORG" type="java.lang.String"/>
		<property name="modyDep" column="MODY_DEP" type="java.lang.String"/>
		<property name="modyUser" column="MODY_USER" type="java.lang.String"/>
		<property name="modyTime" column="MODY_TIME" type="java.util.Date"/>
		<property name="state" column="STATE" type="java.lang.String"/>
		<property name="dataSource" column="DATA_SOURCE" type="java.lang.String"/>
		<property name="dataModyTime" column="DATA_MODY_TIME" type="java.util.Date"/>
		<property name="ksfzrUser" column="KSFZR_USER" type="java.lang.String"/>
		<property name="ksfzrRemark" column="KSFZR_REMARK" type="java.lang.String"/>
		<property name="ksfzrDate" column="KSFZR_DATE" type="java.util.Date"/>
		<property name="kjwscUser" column="KJWSC_USER" type="java.lang.String"/>
		<property name="kjwscReamrk" column="KJWSC_REAMRK" type="java.lang.String"/>
		<property name="kjwscDate" column="KJWSC_DATE" type="java.util.Date"/>
		<property name="sbgsUser" column="SBGS_USER" type="java.lang.String"/>
		<property name="sbgsRemark" column="SBGS_REMARK" type="java.lang.String"/>
		<property name="sbgsDate" column="SBGS_DATE" type="java.util.Date"/>
		<property name="zgsldUser" column="ZGSLD_USER" type="java.lang.String"/>
		<property name="zgsldRemark" column="ZGSLD_REMARK" type="java.lang.String"/>
		<property name="zgsldDate" column="ZGSLD_DATE" type="java.util.Date"/>
		<property name="szUser" column="SZ_USER" type="java.lang.String"/>
		<property name="szRemark" column="SZ_REMARK" type="java.lang.String"/>
		<property name="szDate" column="SZ_DATE" type="java.util.Date"/>
		<property name="dwsjUser" column="DWSJ_USER" type="java.lang.String"/>
		<property name="dwsjRemark" column="DWSJ_REMARK" type="java.lang.String"/>
		<property name="dwsjDate" column="DWSJ_DATE" type="java.util.Date"/>
		<property name="rscUser" column="RSC_USER" type="java.lang.String"/>
		<property name="rscRemark" column="RSC_REMARK" type="java.lang.String"/>
		<property name="rscDate" column="RSC_DATE" type="java.util.Date"/>
		<property name="bmbUser" column="BMB_USER" type="java.lang.String"/>
		<property name="bmbRemark" column="BMB_REMARK" type="java.lang.String"/>
		<property name="bmbDate" column="BMB_DATE" type="java.util.Date"/>
	</class>
</hibernate-mapping>