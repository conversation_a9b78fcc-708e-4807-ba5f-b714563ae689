<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.0//EN"
	"http://struts.apache.org/dtds/struts-2.0.dtd">
<struts>
	<package name="personalOut" extends="bsp-default" namespace="/cn/com/sinosoft/rs/personalout">
		<!-- 查询 -->
		<action name="qryPersonalOut" class="personalOutAction" method="qryParentInput">
			<param name="sessionGroup">personalOut</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/rs/personalout/qryPersonalOut.jsp
			</result>
		</action>
		<action name="qryPersonalOutList" class="commonQueryAction">
			<param name="sessionGroup">personalOut</param>
			<param name="queryCode">QRY_RS_PERSONAL_OUT</param>
			<param name="resultName">qryList</param>
		</action>
		<!-- 查看 -->
		<action name="personalOut_viewParent" class="personalOutAction" method="viewParent">
			<param name="sessionGroup">personalOut</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/rs/personalout/edtPersonalOut.jsp
			</result>
		</action>
		<!-- 添加 -->
		<action name="personalOut_addParentInput" class="personalOutAction" method="addParentInput">
			<param name="sessionGroup">personalOut</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/rs/personalout/edtPersonalOut.jsp
			</result>
		</action>
		<action name="personalOut_addParentSubmit" class="personalOutAction" method="addParentSubmit">
			<param name="sessionGroup">personalOut</param>
		</action>
		<!-- 修改 -->
		<action name="personalOut_edtParentInput" class="personalOutAction" method="edtParentInput">
			<param name="sessionGroup">personalOut</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/rs/personalout/edtPersonalOut.jsp
			</result>
		</action>
		<action name="personalOut_edtParentSubmit" class="personalOutAction" method="edtParentSubmit">
			<param name="sessionGroup">personalOut</param>
		</action>
		<!-- 删除 -->
		<action name="personalOut_delParentSubmit" class="personalOutAction" method="delParentSubmit">
			<param name="sessionGroup">personalOut</param>
		</action> 
		
		<!-- 打开审批页面 -->
		<action name="personalOut_auditParentInput" class="personalOutAction" method="auditParentInput">
			<param name="sessionGroup">personalOut</param>
			<result name="success">
			    /WEB-INF/pages/cn/com/sinosoft/rs/personalout/edtPersonalOut.jsp
			</result>
		</action>
		<action name="personalOutSubmit" class="personalOutAction" method="doAudit">
			<param name="sessionGroup">personalOut</param>
		</action>
		<!-- 撤销申请 -->
		<action name="personalOut_revokeApply" class="personalOutAction" method="revokeApply">
			<param name="sessionGroup">personalOut</param>
		</action>
		<!-- 提交申请 -->
		<action name="personalOut_submitApply" class="personalOutAction" method="submitApply">
			<param name="sessionGroup">personalOut</param>
		</action>
	</package>
</struts>