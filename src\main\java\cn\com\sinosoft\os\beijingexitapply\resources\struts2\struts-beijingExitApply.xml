<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.0//EN"
	"http://struts.apache.org/dtds/struts-2.0.dtd">
<struts>
	<package name="beijingExitApply" extends="bsp-default" namespace="/cn/com/sinosoft/os/beijingexitapply">
		<!-- 查询页面 -->
		<action name="beijingExitApply_qryParentInput" class="beijingExitApplyAction" method="query">
			<param name="sessionGroup">beijingExitApply</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/beijingexitapply/qryBeijingExitApply.jsp
			</result>
		</action>
		
		<!-- 查询数据 -->
		<action name="beijingExitApply_qryParentSubmit" class="commonQueryAction">
			<param name="sessionGroup">beijingExitApply</param>
			<param name="queryCode">QRY_OS_BEIJING_EXIT_APPLY</param>
			<param name="resultName">qryList</param>
		</action>
		
		<!-- 查看 -->
		<action name="beijingExitApply_viewParentInput" class="beijingExitApplyAction" method="view">
			<param name="sessionGroup">beijingExitApply</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/beijingexitapply/edtBeijingExitApply.jsp
			</result>
		</action>
		
		<!-- 新增页面 -->
		<action name="beijingExitApply_addParentInput" class="beijingExitApplyAction" method="add">
			<param name="sessionGroup">beijingExitApply</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/beijingexitapply/edtBeijingExitApply.jsp
			</result>
		</action>
		
		<!-- 新增提交 -->
		<action name="beijingExitApply_addParentSubmit" class="beijingExitApplyAction" method="save">
			<param name="sessionGroup">beijingExitApply</param>
		</action>
		
		<!-- 编辑页面 -->
		<action name="beijingExitApply_edtParentInput" class="beijingExitApplyAction" method="edit">
			<param name="sessionGroup">beijingExitApply</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/beijingexitapply/edtBeijingExitApply.jsp
			</result>
		</action>
		
		<!-- 编辑提交 -->
		<action name="beijingExitApply_edtParentSubmit" class="beijingExitApplyAction" method="save">
			<param name="sessionGroup">beijingExitApply</param>
		</action>
		
		<!-- 删除 -->
		<action name="beijingExitApply_deleteParentSubmit" class="beijingExitApplyAction" method="delete">
			<param name="sessionGroup">beijingExitApply</param>
		</action>
		
		<!-- 审批提交 -->
		<action name="beijingExitApply_auditSubmit" class="beijingExitApplyAction" method="auditSubmit">
			<param name="sessionGroup">beijingExitApply</param>
		</action>
		
		<!-- 导出 -->
		<action name="beijingExitApply_exportParentSubmit" class="commonExportAction">
			<param name="sessionGroup">beijingExitApply</param>
			<param name="queryCode">QRY_OS_BEIJING_EXIT_APPLY</param>
			<param name="fileName">出京申请</param>
		</action>
	</package>
</struts>
