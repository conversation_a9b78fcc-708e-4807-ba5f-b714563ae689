package cn.com.sinosoft.os.beijingexitapply.action;

import ie.bsp.frame.action.BaseEditAction;
import ie.bsp.frame.exception.GeneralException;
import cn.com.sinosoft.os.beijingexitapply.model.BeijingExitApply;
import cn.com.sinosoft.os.beijingexitapply.service.BeijingExitApplyService;
import ie.weaf.toolkit.Util;
import ie.bsp.frame.exception.GeneralExceptionHandler;

/**
 * 出京申请 - Action.
 *
 * <AUTHOR>
 * @date: 2024/01/01 10:00:00
 * @version V1.0
 */
public class BeijingExitApplyAction extends BaseEditAction {

    /**
     * 默认构造.
     */
    public BeijingExitApplyAction() {
        moduleId = "*********";
    }

    // serialVersionUID.
    private static final long serialVersionUID = 1L;

    // 出京申请 - 接口.
    private BeijingExitApplyService service;

    // 出京申请 - 数据.
    private BeijingExitApply result;

    // 主键.
    private String id;
    
    // 任务id
    private String taskid;
    
    // 流程id
    private String piId;
    
    // 流程节点
    private String parama;

    public String getTaskid() {
        return taskid;
    }

    public void setTaskid(String taskid) {
        this.taskid = taskid;
    }

    public String getPiId() {
        return piId;
    }

    public void setPiId(String piId) {
        this.piId = piId;
    }

    public String getParama() {
        return parama;
    }

    public void setParama(String parama) {
        this.parama = parama;
    }

    /**
     * 获取 出京申请 - 接口.
     *
     * @return 出京申请 - 接口
     */
    public BeijingExitApplyService getService() {
        return service;
    }

    /**
     * 设置 出京申请 - 接口.
     *
     * @param service 出京申请 - 接口
     */
    public void setService(BeijingExitApplyService service) {
        this.service = service;
    }

    /**
     * 获取 出京申请 - 数据.
     *
     * @return 出京申请 - 数据
     */
    public BeijingExitApply getResult() {
        return result;
    }

    /**
     * 设置 出京申请 - 数据.
     *
     * @param result 出京申请 - 数据
     */
    public void setResult(BeijingExitApply result) {
        this.result = result;
    }

    /**
     * 获取 主键.
     *
     * @return 主键
     */
    public String getId() {
        return id;
    }

    /**
     * 设置 主键.
     *
     * @param id 主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 出京申请 - 新增页面.
     *
     * @return 新增页面
     */
    public String add() {
        try {
            result = new BeijingExitApply();
        } catch (Exception e) {
            GeneralExceptionHandler.resolveException(request, e);
        }
        return "add";
    }

    /**
     * 出京申请 - 编辑页面.
     *
     * @return 编辑页面
     */
    public String edit() {
        try {
            if (!Util.isNullOrEmpty(id)) {
                result = service.get(id);
            } else if (!Util.isNullOrEmpty(piId)) {
                result = service.getByPiId(piId);
            } else {
                result = new BeijingExitApply();
            }
        } catch (Exception e) {
            GeneralExceptionHandler.resolveException(request, e);
        }
        return "edit";
    }

    /**
     * 出京申请 - 查看页面.
     *
     * @return 查看页面
     */
    public String view() {
        try {
            if (!Util.isNullOrEmpty(id)) {
                result = service.get(id);
            } else if (!Util.isNullOrEmpty(piId)) {
                result = service.getByPiId(piId);
            }
        } catch (Exception e) {
            GeneralExceptionHandler.resolveException(request, e);
        }
        return "view";
    }

    /**
     * 出京申请 - 保存.
     *
     * @return 保存结果
     */
    public String save() {
        try {
            if (Util.isNullOrEmpty(result.getId())) {
                service.save(result);
            } else {
                service.edit(result);
            }
            
            String saveType = request.getParameter("saveType");
            if ("1".equals(saveType)) {
                // 启动工作流
                service.startWorkflow(result);
            }
            
            setReturnMessage("保存成功！");
        } catch (Exception e) {
            GeneralExceptionHandler.resolveException(request, e);
        }
        return "save";
    }

    /**
     * 出京申请 - 删除.
     *
     * @return 删除结果
     */
    public String delete() {
        try {
            service.delete(id);
            setReturnMessage("删除成功！");
        } catch (Exception e) {
            GeneralExceptionHandler.resolveException(request, e);
        }
        return "delete";
    }

    /**
     * 出京申请 - 审批提交.
     *
     * @return 审批结果
     */
    public String auditSubmit() {
        try {
            String auditResult = request.getParameter("auditResult");
            
            if ("1".equals(auditResult)) {
                // 同意
                service.completeTask(taskid, result);
                setReturnMessage("审批通过！");
            } else {
                // 驳回
                service.rejectTask(taskid, result);
                setReturnMessage("已驳回申请！");
            }
        } catch (Exception e) {
            GeneralExceptionHandler.resolveException(request, e);
        }
        return "auditSubmit";
    }

    /**
     * 出京申请 - 查询页面.
     *
     * @return 查询页面
     */
    public String query() {
        return "query";
    }

	@Override
	public void doAddParentInput() throws GeneralException {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void doAddParentSubmit() throws GeneralException {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void doAudit() throws GeneralException {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void doDelteParentSubmit() throws GeneralException {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void doEditParentInput() throws GeneralException {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void doEditParentSubmit() throws GeneralException {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void doQryParentInput() throws GeneralException {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void doViewParentSubmit() throws GeneralException {
		// TODO Auto-generated method stub
		
	}

}
