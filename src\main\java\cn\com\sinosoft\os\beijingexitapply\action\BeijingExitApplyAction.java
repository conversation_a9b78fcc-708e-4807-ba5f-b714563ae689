package cn.com.sinosoft.os.beijingexitapply.action;

import ie.bsp.frame.action.BaseEditAction;
import ie.bsp.frame.exception.GeneralException;
import cn.com.sinosoft.os.beijingexitapply.model.BeijingExitApply;
import cn.com.sinosoft.os.beijingexitapply.service.BeijingExitApplyService;
import ie.weaf.toolkit.Util;
import ie.bsp.frame.exception.GeneralExceptionHandler;

/**
 * 出京申请 - Action.
 *
 * <AUTHOR>
 * @date: 2024/01/01 10:00:00
 * @version V1.0
 */
public class BeijingExitApplyAction extends BaseEditAction {

	/**
	 * 默认构造.
	 */
	public BeijingExitApplyAction() {
		moduleId = "*********";
	}

	// serialVersionUID.
	private static final long serialVersionUID = 1L;

	// 出京申请 - 接口.
	private BeijingExitApplyService service;

	// 出京申请 - 数据.
	private BeijingExitApply result;

	// 主键.
	private String id;
	//任务id
	private String taskid;
	//流程id
	private String piId;
	//流程节点
	private String parama;
	
	
	public String getTaskid() {
		return taskid;
	}

	public void setTaskid(String taskid) {
		this.taskid = taskid;
	}

	public String getPiId() {
		return piId;
	}

	public void setPiId(String piId) {
		this.piId = piId;
	}

	public String getParama() {
		return parama;
	}

	public void setParama(String parama) {
		this.parama = parama;
	}

	/**
	 * 获取 出京申请 - 接口.
	 *
	 * @return 出京申请 - 接口
	 */
	public BeijingExitApplyService getService() {
		return service;
	}

	/**
	 * 设置 出京申请 - 接口.
	 *
	 * @param service
	 *			     出京申请 - 接口
	 */
	public void setService(BeijingExitApplyService service) {
		this.service = service;
	}

	/**
	 * 获取 出京申请 - 数据.
	 *
	 * @return 出京申请 - 数据
	 */
	public BeijingExitApply getResult() {
		return result;
	}

	/**
	 * 设置 出京申请 - 数据.
	 *
	 * @param result
	 *			     出京申请 - 数据
	 */
	public void setResult(BeijingExitApply result) {
		this.result = result;
	}

	/**
	 * 获取 主键.
	 *
	 * @return 主键
	 */
	public String getId() {
		return id;
	}

	/**
	 * 设置 主键.
	 *
	 * @param id
	 *			     主键
	 */
	public void setId(String id) {
		this.id = id;
	}

	@Override
	public void doQryParentInput() throws GeneralException {
		
	}

	@Override
	public void doViewParentSubmit() throws GeneralException {
		funcId = "*********01";
		result = service.get(id);
		setPiId(result.getPiId());
	}

	@Override
	public void doDelteParentSubmit() throws GeneralException {
		funcId = "*********04";
		service.delete(id);
	}

	@Override
	public void doAddParentInput() throws GeneralException {
		
	}

	@Override
	public void doAddParentSubmit() throws GeneralException {
		funcId = "*********02";
		service.save(result);
	}

	@Override
	public void doEditParentInput() throws GeneralException {
		doViewParentSubmit();
	}

	@Override
	public void doEditParentSubmit() throws GeneralException {
		funcId = "*********03";
		service.edit(result);
	}

	@Override
	public void doAudit() throws GeneralException {
		funcId = "";
		service.applySubmit(result, parama, taskid, piId);
		 writeToPage("<script>parent.CloseWindow();</script>");
	}
	/**
	 * 
	 * @Title: auditParentInput
	 * @Description: 打开审批页面
	 * @author: wxm
	 * @return: String
	 * @exception：
	 */
	public String auditParentInput(){
		//通过piid获取实体类
		result = service.getPiId(piId);
		return SUCCESS;
	}
	
	/**
	 * 
	 * @Title: revokeApply
	 * @Description: 撤销申请
	 * @author: wxm
	 * @return: void
	 * @exception：
	 */
	public void revokeApply(){
		service.revokeApply(id);
	}
	/**
	 * 
	 * @Title: submitApply
	 * @Description: 提交申请
	 * @author: wxm
	 * @return: void
	 * @exception：
	 */
	public void submitApply(){
		service.submitApply(id);
	}
 
}
