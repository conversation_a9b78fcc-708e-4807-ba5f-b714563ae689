-- =====================================================
-- 出京申请表创建脚本
-- 数据库：Oracle
-- 表名：OS_TRAVEL_APPLY
-- 创建日期：2025-07-31
-- 说明：用于管理员工和学生的出京申请及审批流程
-- =====================================================

-- 删除表（如果存在）
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE OS_TRAVEL_APPLY CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -942 THEN
            RAISE;
        END IF;
END;
/

-- 删除序列（如果存在）
BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE SEQ_OS_TRAVEL_APPLY';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -2289 THEN
            RAISE;
        END IF;
END;
/

-- 创建序列
CREATE SEQUENCE SEQ_OS_TRAVEL_APPLY
    START WITH 1
    INCREMENT BY 1
    NOMAXVALUE
    NOCYCLE
    CACHE 20;

-- 创建出京申请表
CREATE TABLE OS_TRAVEL_APPLY (
    -- 主键ID
    ID VARCHAR2(32) NOT NULL,
    
    -- 基本申请信息
    APPLICANT_NAME VARCHAR2(100) NOT NULL,           -- 申请人姓名
    IDENTITY_TYPE VARCHAR2(20) NOT NULL,             -- 身份类型（学生、职工、中层）
    DEPARTMENT VARCHAR2(200),                        -- 所属科室
    APPLY_DATE DATE DEFAULT SYSDATE,                 -- 申请日期
    START_DATE DATE NOT NULL,                        -- 起始日期
    END_DATE DATE NOT NULL,                          -- 结束日期
    TRAVEL_DAYS NUMBER(3) DEFAULT 0,                 -- 出京天数
    DESTINATION VARCHAR2(500) NOT NULL,              -- 目的地
    TRAVEL_REASON CLOB NOT NULL,                     -- 出京事由
    
    -- 工作流相关字段
    PI_ID VARCHAR2(64),                              -- 流程实例ID
    TASK_ID VARCHAR2(64),                            -- 当前任务ID
    BUSINESS_KEY VARCHAR2(100),                      -- 业务键
    PROCESS_DEF_KEY VARCHAR2(100),                   -- 流程定义键
    AUDIT_STATE VARCHAR2(2) DEFAULT '0',             -- 审批状态（0-草稿，1-审批中，2-已通过，3-已驳回）
    
    -- 审批相关字段
    DEPT_LEADER VARCHAR2(100),                       -- 审批人
    AUDIT_OPINION CLOB,                              -- 审批意见
    
    -- 系统字段（参考新公章申请）
    ADD_ZONE VARCHAR2(6),                            -- 添加地区
    ADD_ORG VARCHAR2(6),                             -- 添加机构
    ADD_DEP VARCHAR2(6),                             -- 添加科室
    ADD_USER VARCHAR2(30),                           -- 添加人
    ADD_TIME DATE DEFAULT SYSDATE,                   -- 添加时间
    MODY_ZONE VARCHAR2(6),                           -- 修改地区
    MODY_ORG VARCHAR2(6),                            -- 修改机构
    MODY_DEP VARCHAR2(6),                            -- 修改科室
    MODY_USER VARCHAR2(30),                          -- 修改人
    MODY_TIME DATE,                                  -- 修改时间
    STATE VARCHAR2(1) DEFAULT '1',                   -- 记录状态（1-有效，0-无效）
    DATA_SOURCE VARCHAR2(100),                       -- 数据来源
    DATA_MODY_TIME DATE,                             -- 数据修改时间
    
    -- 扩展字段（预留）
    EXTEND_FIELD1 VARCHAR2(200),                     -- 扩展字段1
    EXTEND_FIELD2 VARCHAR2(200),                     -- 扩展字段2
    EXTEND_FIELD3 VARCHAR2(200),                     -- 扩展字段3
    EXTEND_FIELD4 VARCHAR2(200),                     -- 扩展字段4
    EXTEND_FIELD5 VARCHAR2(200),                     -- 扩展字段5
    
    -- 版本控制
    VERSION_NO NUMBER(10) DEFAULT 0,                 -- 版本号
    
    -- 主键约束
    CONSTRAINT PK_OS_TRAVEL_APPLY PRIMARY KEY (ID)
);

-- 添加表注释
COMMENT ON TABLE OS_TRAVEL_APPLY IS '出京申请表 - 用于管理员工和学生的出京申请及审批流程';

-- 添加字段注释
COMMENT ON COLUMN OS_TRAVEL_APPLY.ID IS '主键ID - 唯一标识符';
COMMENT ON COLUMN OS_TRAVEL_APPLY.APPLICANT_NAME IS '申请人姓名 - 提交申请的人员姓名';
COMMENT ON COLUMN OS_TRAVEL_APPLY.IDENTITY_TYPE IS '身份类型 - 学生/职工/中层，用于确定审批流程';
COMMENT ON COLUMN OS_TRAVEL_APPLY.DEPARTMENT IS '所属科室 - 申请人所在的部门或科室';
COMMENT ON COLUMN OS_TRAVEL_APPLY.APPLY_DATE IS '申请日期 - 提交申请的日期';
COMMENT ON COLUMN OS_TRAVEL_APPLY.START_DATE IS '起始日期 - 出京开始日期';
COMMENT ON COLUMN OS_TRAVEL_APPLY.END_DATE IS '结束日期 - 出京结束日期';
COMMENT ON COLUMN OS_TRAVEL_APPLY.TRAVEL_DAYS IS '出京天数 - 根据起始和结束日期自动计算';
COMMENT ON COLUMN OS_TRAVEL_APPLY.DESTINATION IS '目的地 - 出京的目标地点';
COMMENT ON COLUMN OS_TRAVEL_APPLY.TRAVEL_REASON IS '出京事由 - 详细说明出京的原因和目的';
COMMENT ON COLUMN OS_TRAVEL_APPLY.PI_ID IS '流程实例ID - Activiti工作流的流程实例标识';
COMMENT ON COLUMN OS_TRAVEL_APPLY.TASK_ID IS '当前任务ID - Activiti工作流的当前任务标识';
COMMENT ON COLUMN OS_TRAVEL_APPLY.BUSINESS_KEY IS '业务键 - 工作流业务关联键';
COMMENT ON COLUMN OS_TRAVEL_APPLY.PROCESS_DEF_KEY IS '流程定义键 - 工作流流程定义标识';
COMMENT ON COLUMN OS_TRAVEL_APPLY.AUDIT_STATE IS '审批状态 - 0:草稿 1:审批中 2:已通过 3:已驳回';
COMMENT ON COLUMN OS_TRAVEL_APPLY.DEPT_LEADER IS '审批人 - 负责审批的领导姓名';
COMMENT ON COLUMN OS_TRAVEL_APPLY.AUDIT_OPINION IS '审批意见 - 审批人的审批意见和建议';
COMMENT ON COLUMN OS_TRAVEL_APPLY.ADD_ZONE IS '添加地区 - 创建记录的地区代码';
COMMENT ON COLUMN OS_TRAVEL_APPLY.ADD_ORG IS '添加机构 - 创建记录的机构代码';
COMMENT ON COLUMN OS_TRAVEL_APPLY.ADD_DEP IS '添加科室 - 创建记录的科室代码';
COMMENT ON COLUMN OS_TRAVEL_APPLY.ADD_USER IS '添加人 - 创建记录的用户';
COMMENT ON COLUMN OS_TRAVEL_APPLY.ADD_TIME IS '添加时间 - 记录创建时间';
COMMENT ON COLUMN OS_TRAVEL_APPLY.MODY_ZONE IS '修改地区 - 最后修改记录的地区代码';
COMMENT ON COLUMN OS_TRAVEL_APPLY.MODY_ORG IS '修改机构 - 最后修改记录的机构代码';
COMMENT ON COLUMN OS_TRAVEL_APPLY.MODY_DEP IS '修改科室 - 最后修改记录的科室代码';
COMMENT ON COLUMN OS_TRAVEL_APPLY.MODY_USER IS '修改人 - 最后修改记录的用户';
COMMENT ON COLUMN OS_TRAVEL_APPLY.MODY_TIME IS '修改时间 - 记录最后修改时间';
COMMENT ON COLUMN OS_TRAVEL_APPLY.STATE IS '记录状态 - 1:有效 0:无效（逻辑删除标识）';
COMMENT ON COLUMN OS_TRAVEL_APPLY.DATA_SOURCE IS '数据来源 - 记录数据的来源系统';
COMMENT ON COLUMN OS_TRAVEL_APPLY.DATA_MODY_TIME IS '数据修改时间 - 数据最后修改时间';
COMMENT ON COLUMN OS_TRAVEL_APPLY.EXTEND_FIELD1 IS '扩展字段1 - 预留扩展字段';
COMMENT ON COLUMN OS_TRAVEL_APPLY.EXTEND_FIELD2 IS '扩展字段2 - 预留扩展字段';
COMMENT ON COLUMN OS_TRAVEL_APPLY.EXTEND_FIELD3 IS '扩展字段3 - 预留扩展字段';
COMMENT ON COLUMN OS_TRAVEL_APPLY.EXTEND_FIELD4 IS '扩展字段4 - 预留扩展字段';
COMMENT ON COLUMN OS_TRAVEL_APPLY.EXTEND_FIELD5 IS '扩展字段5 - 预留扩展字段';
COMMENT ON COLUMN OS_TRAVEL_APPLY.VERSION_NO IS '版本号 - 用于乐观锁控制';

-- 创建索引
CREATE INDEX IDX_TRAVEL_APPLY_STATE ON OS_TRAVEL_APPLY(AUDIT_STATE);
CREATE INDEX IDX_TRAVEL_APPLY_USER ON OS_TRAVEL_APPLY(ADD_USER);
CREATE INDEX IDX_TRAVEL_APPLY_DATE ON OS_TRAVEL_APPLY(APPLY_DATE);
CREATE INDEX IDX_TRAVEL_APPLY_PIID ON OS_TRAVEL_APPLY(PI_ID);
CREATE INDEX IDX_TRAVEL_APPLY_IDENTITY ON OS_TRAVEL_APPLY(IDENTITY_TYPE);
CREATE INDEX IDX_TRAVEL_APPLY_DEPT ON OS_TRAVEL_APPLY(DEPARTMENT);
CREATE INDEX IDX_TRAVEL_APPLY_STATUS ON OS_TRAVEL_APPLY(STATE);
CREATE INDEX IDX_TRAVEL_APPLY_START_DATE ON OS_TRAVEL_APPLY(START_DATE);

-- 添加约束
ALTER TABLE OS_TRAVEL_APPLY ADD CONSTRAINT CHK_TRAVEL_APPLY_STATE 
    CHECK (AUDIT_STATE IN ('0', '1', '2', '3'));

ALTER TABLE OS_TRAVEL_APPLY ADD CONSTRAINT CHK_TRAVEL_APPLY_IDENTITY 
    CHECK (IDENTITY_TYPE IN ('学生', '职工', '中层'));

ALTER TABLE OS_TRAVEL_APPLY ADD CONSTRAINT CHK_TRAVEL_APPLY_STATUS 
    CHECK (STATE IN ('0', '1'));

ALTER TABLE OS_TRAVEL_APPLY ADD CONSTRAINT CHK_TRAVEL_APPLY_DATES 
    CHECK (END_DATE >= START_DATE);

ALTER TABLE OS_TRAVEL_APPLY ADD CONSTRAINT CHK_TRAVEL_APPLY_DAYS 
    CHECK (TRAVEL_DAYS >= 0);

-- 创建触发器：自动生成ID
CREATE OR REPLACE TRIGGER TRG_OS_TRAVEL_APPLY_ID
    BEFORE INSERT ON OS_TRAVEL_APPLY
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        :NEW.ID := 'TA' || TO_CHAR(SYSDATE, 'YYYYMMDD') || LPAD(SEQ_OS_TRAVEL_APPLY.NEXTVAL, 6, '0');
    END IF;
END;
/

-- 创建触发器：自动更新修改时间和版本号
CREATE OR REPLACE TRIGGER TRG_OS_TRAVEL_APPLY_UPDATE
    BEFORE UPDATE ON OS_TRAVEL_APPLY
    FOR EACH ROW
BEGIN
    :NEW.MODY_TIME := SYSDATE;
    :NEW.DATA_MODY_TIME := SYSDATE;
    :NEW.VERSION_NO := :OLD.VERSION_NO + 1;
END;
/

-- 插入测试数据（可选）
INSERT INTO OS_TRAVEL_APPLY (
    ID, APPLICANT_NAME, IDENTITY_TYPE, DEPARTMENT, APPLY_DATE, 
    START_DATE, END_DATE, TRAVEL_DAYS, DESTINATION, TRAVEL_REASON,
    AUDIT_STATE, ADD_USER, ADD_TIME, STATE
) VALUES (
    'TA20250731000001', '张三', '职工', '信息技术部', SYSDATE,
    DATE '2025-08-01', DATE '2025-08-03', 3, '上海', '参加技术培训会议',
    '0', 'admin', SYSDATE, '1'
);

INSERT INTO OS_TRAVEL_APPLY (
    ID, APPLICANT_NAME, IDENTITY_TYPE, DEPARTMENT, APPLY_DATE, 
    START_DATE, END_DATE, TRAVEL_DAYS, DESTINATION, TRAVEL_REASON,
    AUDIT_STATE, ADD_USER, ADD_TIME, STATE
) VALUES (
    'TA20250731000002', '李四', '学生', '计算机学院', SYSDATE,
    DATE '2025-08-05', DATE '2025-08-07', 3, '深圳', '参加学术会议',
    '1', 'student01', SYSDATE, '1'
);

-- 提交事务
COMMIT;

-- 验证表创建
SELECT COUNT(*) AS RECORD_COUNT FROM OS_TRAVEL_APPLY;

-- 显示表结构
DESC OS_TRAVEL_APPLY;

-- 显示表注释
SELECT TABLE_NAME, COMMENTS FROM USER_TAB_COMMENTS WHERE TABLE_NAME = 'OS_TRAVEL_APPLY';

-- 显示字段注释
SELECT COLUMN_NAME, COMMENTS FROM USER_COL_COMMENTS WHERE TABLE_NAME = 'OS_TRAVEL_APPLY' ORDER BY COLUMN_ID;
