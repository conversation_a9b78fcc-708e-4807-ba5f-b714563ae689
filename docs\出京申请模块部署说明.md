# 出京申请模块部署说明

## 概述
本文档描述了出京申请模块的部署步骤和配置说明。该模块基于现有的新公章申请模块架构开发，集成了Activiti工作流引擎。

## 模块结构

### 1. 数据库层
- **表名**: `OS_TRAVEL_APPLY`
- **SQL文件**: `database/OS_TRAVEL_APPLY.sql`
- **字段说明**:
  - 基本信息：申请人姓名、身份类型、科室、申请日期等
  - 出京信息：起始日期、结束日期、天数、目的地、事由
  - 工作流字段：流程实例ID、审批状态、审批意见等
  - 系统字段：添加人、添加时间、修改人、修改时间等

### 2. 实体层
- **实体类**: `cn.com.sinosoft.os.travelapply.model.TravelApply`
- **Hibernate映射**: `TravelApply.hbm.xml`

### 3. 业务层
- **Service接口**: `cn.com.sinosoft.os.travelapply.service.TravelApplyService`
- **Service实现**: `cn.com.sinosoft.os.travelapply.service.impl.TravelApplyServiceImpl`

### 4. 控制层
- **Action类**: `cn.com.sinosoft.os.travelapply.action.TravelApplyAction`

### 5. 工作流层
- **BPMN文件**: `cn/com/sinosoft/os/travelapply/bpmn/travelApply.bpmn`
- **流程定义**: 支持不同身份类型的审批路径

### 6. 配置层
- **Spring Service配置**: `applicationContext-service.xml`
- **Spring Action配置**: `applicationContext-action.xml`
- **Struts2配置**: `struts-travelApply.xml`

### 7. 视图层
- **查询页面**: `qryParent.jsp`
- **新增页面**: `addParent.jsp`
- **修改页面**: `edtParent.jsp`
- **查看页面**: `viewParent.jsp`
- **审批页面**: `auditParent.jsp`

## 部署步骤

### 1. 数据库部署
```sql
-- 执行数据库脚本
@database/OS_TRAVEL_APPLY.sql
```

### 2. 代码部署
所有代码文件已按照项目结构放置在相应目录中：
- Java源码：`src/main/java/cn/com/sinosoft/os/travelapply/`
- JSP页面：`src/main/webapp/WEB-INF/jsp/travelapply/`
- 配置文件：各模块resources目录下

### 3. 配置文件更新
- `struts-os-include.xml` 已更新，包含新模块的Struts配置引用
- Spring配置文件会被自动加载（根据web.xml中的配置模式）

### 4. 工作流部署
- BPMN文件位于：`src/main/java/cn/com/sinosoft/os/travelapply/bpmn/travelApply.bpmn`
- 需要在Activiti中部署该流程定义

## 功能特性

### 1. 基本功能
- ✅ 申请信息录入（姓名、身份、科室、日期、目的地、事由等）
- ✅ 申请信息查询和列表展示
- ✅ 申请信息修改（草稿和驳回状态）
- ✅ 申请信息删除
- ✅ 申请详情查看

### 2. 工作流功能
- ✅ 申请提交并启动工作流
- ✅ 根据身份类型自动路由审批人
- ✅ 审批操作（同意/驳回）
- ✅ 申请撤回功能
- ✅ 审批状态跟踪

### 3. 业务规则
- **学生**: 科室主任审批
- **职工**: 科室主任审批  
- **中层**: 所领导审批
- **天数计算**: 自动根据起始和结束日期计算
- **状态管理**: 草稿(0) → 审批中(1) → 已通过(2)/已驳回(3)

### 4. 扩展功能
- ✅ Excel导出功能框架
- ✅ 扩展字段预留
- ✅ 审批意见记录

## 访问地址

部署完成后，可通过以下地址访问：
- 查询页面: `/travelApply/qryParentInput.action`
- 新增申请: `/travelApply/addParentInput.action`

## 注意事项

1. **数据库权限**: 确保应用用户对OS_TRAVEL_APPLY表有完整的CRUD权限
2. **工作流引擎**: 确保Activiti引擎正常运行
3. **用户权限**: 需要配置相应的菜单权限和功能权限
4. **审批人配置**: 需要根据实际组织架构配置审批人逻辑
5. **日期组件**: 确保My97DatePicker日期选择组件可用
6. **样式文件**: 确保相关CSS和JS文件路径正确

## 后续优化建议

1. **审批人动态配置**: 根据实际组织架构动态确定审批人
2. **Excel导出完善**: 完善Excel导出功能的具体实现
3. **消息通知**: 集成消息通知功能，审批状态变更时通知相关人员
4. **统计报表**: 添加出京申请的统计分析功能
5. **移动端适配**: 考虑移动端访问的响应式设计

## 技术支持

如有问题，请联系开发团队或查阅相关技术文档。
