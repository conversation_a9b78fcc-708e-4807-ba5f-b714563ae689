package cn.com.sinosoft.os.beijingexitapply.service.impl;

import ie.bsp.core.bean.UserView;
import ie.bsp.frame.dao.CommonBaseDao;
import ie.bsp.ui.FrameConstant;
import ie.weaf.toolkit.Util;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.struts2.ServletActionContext;

import cn.com.sinosoft.os.constant.OsConstant;
import cn.com.sinosoft.os.beijingexitapply.model.BeijingExitApply;
import cn.com.sinosoft.os.beijingexitapply.service.BeijingExitApplyService;

/**
 * 出京申请 - service接口实现.
 *
 * <AUTHOR>
 * @date: 2024/01/01 10:00:00
 * @version V1.0
 */
public class BeijingExitApplyServiceImpl implements BeijingExitApplyService {

    // 通用dao.
    private CommonBaseDao dao;

    /**
     * 获取 通用dao.
     *
     * @return 通用dao
     */
    public CommonBaseDao getDao() {
        return dao;
    }

    /**
     * 设置 通用dao.
     *
     * @param dao 通用dao
     */
    public void setDao(CommonBaseDao dao) {
        this.dao = dao;
    }

    @Override
    public BeijingExitApply get(String id) {
        return (BeijingExitApply) dao.get(BeijingExitApply.class, id);
    }

    @Override
    public void delete(String id) {
        String[] arrId = id.split(",");

        for (int i = 0; i < arrId.length; i++) {
            dao.excuteSql("update OS_BEIJING_EXIT_APPLY set STATE='0',AUDIT_STATE='"
                    +  "' where 1=1 "
                    + " and ID = '" + arrId[i] + "'");
        }
    }

    @Override
    public void save(BeijingExitApply result) {
        UserView user = (UserView) ServletActionContext.getRequest()
                .getSession().getAttribute(FrameConstant.SESSION_USERVIEW);
        result.setAddZone(user.getZonecode());
        result.setAddOrg(user.getOrgcode());
        result.setAddDep(user.getDepartmentId());
        result.setAddUser(user.getUsername());
        result.setAddTime(new Date());
        result.setState("1");
        result.setId(ie.bsp.util.UUID.randomUUID().toString());
        result.setAuditState(OsConstant.AUDIT_STATE_ONE); // 草稿状态
        result.setApplyDate(new Date());
        
        // 计算出京天数
        if (result.getStartDate() != null && result.getEndDate() != null) {
            long diffInMillies = result.getEndDate().getTime() - result.getStartDate().getTime();
            long diffInDays = diffInMillies / (24 * 60 * 60 * 1000);
            result.setTravelDays((int) diffInDays + 1);
        }
        
        dao.save(result);
    }

    @Override
    public void edit(BeijingExitApply result) {
        UserView user = (UserView) ServletActionContext.getRequest()
                .getSession().getAttribute(FrameConstant.SESSION_USERVIEW);
        result.setModyZone(user.getZonecode());
        result.setModyOrg(user.getOrgcode());
        result.setModyDep(user.getDepartmentId());
        result.setModyUser(user.getUsername());
        result.setModyTime(new Date());
        
        // 重新计算出京天数
        if (result.getStartDate() != null && result.getEndDate() != null) {
            long diffInMillies = result.getEndDate().getTime() - result.getStartDate().getTime();
            long diffInDays = diffInMillies / (24 * 60 * 60 * 1000);
            result.setTravelDays((int) diffInDays + 1);
        }
        
        dao.edit(result);
    }

    @Override
    public BeijingExitApply getByPiId(String piId) {
        String hql = "from BeijingExitApply b where b.piId = '" + piId + "'";
        List<BeijingExitApply> list = dao.qryByHql(hql);
        if (list != null && !list.isEmpty()) {
            return list.iterator().next();
        }
        return null;
    }

    @Override
    public String startWorkflow(BeijingExitApply result) {
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("applyUserId", result.getAddUser());
        variables.put("identity", result.getIdentityType());
        variables.put("approved", false);
        
        // 启动工作流程
        result.setProcessDefKey("beijingExitApply");
        result.setBusinessKey(result.getId());
        result.setAuditState(OsConstant.AUDIT_STATE_TWO); // 审批中
        
        // 这里需要调用工作流服务启动流程
        // String processInstanceId = workflowService.startProcess("beijingExitApply", result.getId(), variables);
        // result.setPiId(processInstanceId);
        
        dao.edit(result);
        return result.getPiId();
    }

    @Override
    public void completeTask(String taskId, BeijingExitApply result) {
        UserView user = (UserView) ServletActionContext.getRequest()
                .getSession().getAttribute(FrameConstant.SESSION_USERVIEW);
        
        result.setModyUser(user.getUsername());
        result.setModyTime(new Date());
        result.setDeptLeader(user.getUsername());
        
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("approved", true);
        variables.put("auditOpinion", result.getAuditOpinion());
        
        // 这里需要调用工作流服务完成任务
        // workflowService.completeTask(taskId, variables);
        
        dao.edit(result);
    }

    @Override
    public void rejectTask(String taskId, BeijingExitApply result) {
        UserView user = (UserView) ServletActionContext.getRequest()
                .getSession().getAttribute(FrameConstant.SESSION_USERVIEW);
        
        result.setModyUser(user.getUsername());
        result.setModyTime(new Date());
        result.setDeptLeader(user.getUsername());
        result.setAuditState(OsConstant.AUDIT_STATE_REVOKE); // 已驳回
        
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("approved", false);
        variables.put("auditOpinion", result.getAuditOpinion());
        
        // 这里需要调用工作流服务驳回任务
        // workflowService.completeTask(taskId, variables);
        
        dao.edit(result);
    }

    @Override
    public BeijingExitApply getPiId(String piId) {
        return getByPiId(piId);
    }

    @Override
    public void applySubmit(BeijingExitApply result, String parama, String taskid, String piId) {
        // 简化的审批处理逻辑，实际项目中需要根据具体业务需求实现
        String auditResult = ServletActionContext.getRequest().getParameter("auditResult");
        String auditRemark = ServletActionContext.getRequest().getParameter("auditRemark");

        if ("1".equals(auditResult)) {
            // 同意
            result.setAuditOpinion(auditRemark);
            completeTask(taskid, result);
        } else {
            // 驳回
            result.setAuditOpinion(auditRemark);
            rejectTask(taskid, result);
        }
    }

    @Override
    public void revokeApply(String id) {
        if (!Util.isNullOrEmpty(id)) {
            String[] ids = id.split(",");
            for (int i = 0; i < ids.length; i++) {
                BeijingExitApply result = get(ids[i]);
                if (result != null) {
                    // 删除流程实例
                    if (!Util.isNullOrEmpty(result.getPiId())) {
                        // workflowService.deleteProcessInstance(result.getPiId());
                    }
                    result.setPiId(null);
                    result.setAuditState(OsConstant.AUDIT_STATE_DRAFT); // 草稿状态
                    dao.edit(result);
                }
            }
        }
    }

    @Override
    public void submitApply(String id) {
        if (!Util.isNullOrEmpty(id)) {
            String[] ids = id.split(",");
            for (int i = 0; i < ids.length; i++) {
                BeijingExitApply result = get(ids[i]);
                if (result != null) {
                    startWorkflow(result);
                    dao.edit(result);
                }
            }
        }
    }

}
