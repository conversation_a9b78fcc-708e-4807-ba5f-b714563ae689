package cn.com.sinosoft.os.beijingexitapply.model;

import java.util.Date;

/**
 * 出京申请 - 实体类.
 *
 * <AUTHOR>
 * @date: 2024/01/01 10:00:00
 * @version V1.0
 */
public class BeijingExitApply implements java.io.Serializable {

    // serialVersionUID.
    private static final long serialVersionUID = 1L;

    // 主键ID.
    private String id;

    // 申请人姓名.
    private String applicantName;

    // 身份类型.
    private String identityType;

    // 所属科室.
    private String department;

    // 申请日期.
    private Date applyDate;

    // 起始日期.
    private Date startDate;

    // 结束日期.
    private Date endDate;

    // 出京天数.
    private Integer travelDays;

    // 目的地.
    private String destination;

    // 出京事由.
    private String travelReason;

    // 流程实例ID.
    private String piId;

    // 当前任务ID.
    private String taskId;

    // 业务键.
    private String businessKey;

    // 流程定义键.
    private String processDefKey;

    // 审批状态.
    private String auditState;

    // 审批人.
    private String deptLeader;

    // 审批意见.
    private String auditOpinion;

    // 添加地区.
    private String addZone;

    // 添加机构.
    private String addOrg;

    // 添加科室.
    private String addDep;

    // 添加人.
    private String addUser;

    // 添加时间.
    private Date addTime;

    // 修改地区.
    private String modyZone;

    // 修改机构.
    private String modyOrg;

    // 修改科室.
    private String modyDep;

    // 修改人.
    private String modyUser;

    // 修改时间.
    private Date modyTime;

    // 记录状态.
    private String state;

    // 数据来源.
    private String dataSource;

    // 数据修改时间.
    private Date dataModyTime;

    // 扩展字段1.
    private String extendField1;

    // 扩展字段2.
    private String extendField2;

    // 扩展字段3.
    private String extendField3;

    // 扩展字段4.
    private String extendField4;

    // 扩展字段5.
    private String extendField5;

    // 版本号.
    private Integer versionNo;

    /**
     * 获取 主键ID.
     *
     * @return 主键ID
     */
    public String getId() {
        return id;
    }

    /**
     * 设置 主键ID.
     *
     * @param id 主键ID
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取 申请人姓名.
     *
     * @return 申请人姓名
     */
    public String getApplicantName() {
        return applicantName;
    }

    /**
     * 设置 申请人姓名.
     *
     * @param applicantName 申请人姓名
     */
    public void setApplicantName(String applicantName) {
        this.applicantName = applicantName;
    }

    /**
     * 获取 身份类型.
     *
     * @return 身份类型
     */
    public String getIdentityType() {
        return identityType;
    }

    /**
     * 设置 身份类型.
     *
     * @param identityType 身份类型
     */
    public void setIdentityType(String identityType) {
        this.identityType = identityType;
    }

    /**
     * 获取 所属科室.
     *
     * @return 所属科室
     */
    public String getDepartment() {
        return department;
    }

    /**
     * 设置 所属科室.
     *
     * @param department 所属科室
     */
    public void setDepartment(String department) {
        this.department = department;
    }

    /**
     * 获取 申请日期.
     *
     * @return 申请日期
     */
    public Date getApplyDate() {
        return applyDate;
    }

    /**
     * 设置 申请日期.
     *
     * @param applyDate 申请日期
     */
    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    /**
     * 获取 起始日期.
     *
     * @return 起始日期
     */
    public Date getStartDate() {
        return startDate;
    }

    /**
     * 设置 起始日期.
     *
     * @param startDate 起始日期
     */
    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    /**
     * 获取 结束日期.
     *
     * @return 结束日期
     */
    public Date getEndDate() {
        return endDate;
    }

    /**
     * 设置 结束日期.
     *
     * @param endDate 结束日期
     */
    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    /**
     * 获取 出京天数.
     *
     * @return 出京天数
     */
    public Integer getTravelDays() {
        return travelDays;
    }

    /**
     * 设置 出京天数.
     *
     * @param travelDays 出京天数
     */
    public void setTravelDays(Integer travelDays) {
        this.travelDays = travelDays;
    }

    /**
     * 获取 目的地.
     *
     * @return 目的地
     */
    public String getDestination() {
        return destination;
    }

    /**
     * 设置 目的地.
     *
     * @param destination 目的地
     */
    public void setDestination(String destination) {
        this.destination = destination;
    }

    /**
     * 获取 出京事由.
     *
     * @return 出京事由
     */
    public String getTravelReason() {
        return travelReason;
    }

    /**
     * 设置 出京事由.
     *
     * @param travelReason 出京事由
     */
    public void setTravelReason(String travelReason) {
        this.travelReason = travelReason;
    }

    /**
     * 获取 流程实例ID.
     *
     * @return 流程实例ID
     */
    public String getPiId() {
        return piId;
    }

    /**
     * 设置 流程实例ID.
     *
     * @param piId 流程实例ID
     */
    public void setPiId(String piId) {
        this.piId = piId;
    }

    /**
     * 获取 当前任务ID.
     *
     * @return 当前任务ID
     */
    public String getTaskId() {
        return taskId;
    }

    /**
     * 设置 当前任务ID.
     *
     * @param taskId 当前任务ID
     */
    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    /**
     * 获取 业务键.
     *
     * @return 业务键
     */
    public String getBusinessKey() {
        return businessKey;
    }

    /**
     * 设置 业务键.
     *
     * @param businessKey 业务键
     */
    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    /**
     * 获取 流程定义键.
     *
     * @return 流程定义键
     */
    public String getProcessDefKey() {
        return processDefKey;
    }

    /**
     * 设置 流程定义键.
     *
     * @param processDefKey 流程定义键
     */
    public void setProcessDefKey(String processDefKey) {
        this.processDefKey = processDefKey;
    }

    /**
     * 获取 审批状态.
     *
     * @return 审批状态
     */
    public String getAuditState() {
        return auditState;
    }

    /**
     * 设置 审批状态.
     *
     * @param auditState 审批状态
     */
    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    /**
     * 获取 审批人.
     *
     * @return 审批人
     */
    public String getDeptLeader() {
        return deptLeader;
    }

    /**
     * 设置 审批人.
     *
     * @param deptLeader 审批人
     */
    public void setDeptLeader(String deptLeader) {
        this.deptLeader = deptLeader;
    }

    /**
     * 获取 审批意见.
     *
     * @return 审批意见
     */
    public String getAuditOpinion() {
        return auditOpinion;
    }

    /**
     * 设置 审批意见.
     *
     * @param auditOpinion 审批意见
     */
    public void setAuditOpinion(String auditOpinion) {
        this.auditOpinion = auditOpinion;
    }

    /**
     * 获取 添加地区.
     *
     * @return 添加地区
     */
    public String getAddZone() {
        return addZone;
    }

    /**
     * 设置 添加地区.
     *
     * @param addZone 添加地区
     */
    public void setAddZone(String addZone) {
        this.addZone = addZone;
    }

    /**
     * 获取 添加机构.
     *
     * @return 添加机构
     */
    public String getAddOrg() {
        return addOrg;
    }

    /**
     * 设置 添加机构.
     *
     * @param addOrg 添加机构
     */
    public void setAddOrg(String addOrg) {
        this.addOrg = addOrg;
    }

    /**
     * 获取 添加科室.
     *
     * @return 添加科室
     */
    public String getAddDep() {
        return addDep;
    }

    /**
     * 设置 添加科室.
     *
     * @param addDep 添加科室
     */
    public void setAddDep(String addDep) {
        this.addDep = addDep;
    }

    /**
     * 获取 添加人.
     *
     * @return 添加人
     */
    public String getAddUser() {
        return addUser;
    }

    /**
     * 设置 添加人.
     *
     * @param addUser 添加人
     */
    public void setAddUser(String addUser) {
        this.addUser = addUser;
    }

    /**
     * 获取 添加时间.
     *
     * @return 添加时间
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * 设置 添加时间.
     *
     * @param addTime 添加时间
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * 获取 修改地区.
     *
     * @return 修改地区
     */
    public String getModyZone() {
        return modyZone;
    }

    /**
     * 设置 修改地区.
     *
     * @param modyZone 修改地区
     */
    public void setModyZone(String modyZone) {
        this.modyZone = modyZone;
    }

    /**
     * 获取 修改机构.
     *
     * @return 修改机构
     */
    public String getModyOrg() {
        return modyOrg;
    }

    /**
     * 设置 修改机构.
     *
     * @param modyOrg 修改机构
     */
    public void setModyOrg(String modyOrg) {
        this.modyOrg = modyOrg;
    }

    /**
     * 获取 修改科室.
     *
     * @return 修改科室
     */
    public String getModyDep() {
        return modyDep;
    }

    /**
     * 设置 修改科室.
     *
     * @param modyDep 修改科室
     */
    public void setModyDep(String modyDep) {
        this.modyDep = modyDep;
    }

    /**
     * 获取 修改人.
     *
     * @return 修改人
     */
    public String getModyUser() {
        return modyUser;
    }

    /**
     * 设置 修改人.
     *
     * @param modyUser 修改人
     */
    public void setModyUser(String modyUser) {
        this.modyUser = modyUser;
    }

    /**
     * 获取 修改时间.
     *
     * @return 修改时间
     */
    public Date getModyTime() {
        return modyTime;
    }

    /**
     * 设置 修改时间.
     *
     * @param modyTime 修改时间
     */
    public void setModyTime(Date modyTime) {
        this.modyTime = modyTime;
    }

    /**
     * 获取 记录状态.
     *
     * @return 记录状态
     */
    public String getState() {
        return state;
    }

    /**
     * 设置 记录状态.
     *
     * @param state 记录状态
     */
    public void setState(String state) {
        this.state = state;
    }

    /**
     * 获取 数据来源.
     *
     * @return 数据来源
     */
    public String getDataSource() {
        return dataSource;
    }

    /**
     * 设置 数据来源.
     *
     * @param dataSource 数据来源
     */
    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    /**
     * 获取 数据修改时间.
     *
     * @return 数据修改时间
     */
    public Date getDataModyTime() {
        return dataModyTime;
    }

    /**
     * 设置 数据修改时间.
     *
     * @param dataModyTime 数据修改时间
     */
    public void setDataModyTime(Date dataModyTime) {
        this.dataModyTime = dataModyTime;
    }

    /**
     * 获取 扩展字段1.
     *
     * @return 扩展字段1
     */
    public String getExtendField1() {
        return extendField1;
    }

    /**
     * 设置 扩展字段1.
     *
     * @param extendField1 扩展字段1
     */
    public void setExtendField1(String extendField1) {
        this.extendField1 = extendField1;
    }

    /**
     * 获取 扩展字段2.
     *
     * @return 扩展字段2
     */
    public String getExtendField2() {
        return extendField2;
    }

    /**
     * 设置 扩展字段2.
     *
     * @param extendField2 扩展字段2
     */
    public void setExtendField2(String extendField2) {
        this.extendField2 = extendField2;
    }

    /**
     * 获取 扩展字段3.
     *
     * @return 扩展字段3
     */
    public String getExtendField3() {
        return extendField3;
    }

    /**
     * 设置 扩展字段3.
     *
     * @param extendField3 扩展字段3
     */
    public void setExtendField3(String extendField3) {
        this.extendField3 = extendField3;
    }

    /**
     * 获取 扩展字段4.
     *
     * @return 扩展字段4
     */
    public String getExtendField4() {
        return extendField4;
    }

    /**
     * 设置 扩展字段4.
     *
     * @param extendField4 扩展字段4
     */
    public void setExtendField4(String extendField4) {
        this.extendField4 = extendField4;
    }

    /**
     * 获取 扩展字段5.
     *
     * @return 扩展字段5
     */
    public String getExtendField5() {
        return extendField5;
    }

    /**
     * 设置 扩展字段5.
     *
     * @param extendField5 扩展字段5
     */
    public void setExtendField5(String extendField5) {
        this.extendField5 = extendField5;
    }

    /**
     * 获取 版本号.
     *
     * @return 版本号
     */
    public Integer getVersionNo() {
        return versionNo;
    }

    /**
     * 设置 版本号.
     *
     * @param versionNo 版本号
     */
    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

}
