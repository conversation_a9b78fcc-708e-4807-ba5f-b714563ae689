# 出京申请模块完成说明

## 模块概述

出京申请模块已按照项目现有框架完成开发，完全遵循"新公章申请"模块的架构模式。

## 已完成的文件列表

### 1. 实体类和映射
- `src/main/java/cn/com/sinosoft/os/beijingexitapply/model/BeijingExitApply.java` - 实体类
- `src/main/java/cn/com/sinosoft/os/beijingexitapply/model/BeijingExitApply.hbm.xml` - Hibernate映射文件

### 2. 服务层
- `src/main/java/cn/com/sinosoft/os/beijingexitapply/service/BeijingExitApplyService.java` - 服务接口
- `src/main/java/cn/com/sinosoft/os/beijingexitapply/service/impl/BeijingExitApplyServiceImpl.java` - 服务实现

### 3. 控制层
- `src/main/java/cn/com/sinosoft/os/beijingexitapply/action/BeijingExitApplyAction.java` - Action类

### 4. 配置文件
- `src/main/java/cn/com/sinosoft/os/beijingexitapply/resources/spring/applicationContext-action.xml` - Action配置
- `src/main/java/cn/com/sinosoft/os/beijingexitapply/resources/spring/applicationContext-service.xml` - Service配置
- `src/main/java/cn/com/sinosoft/os/beijingexitapply/resources/struts2/struts-beijingExitApply.xml` - Struts配置

### 5. 数据库脚本
- `src/main/java/cn/com/sinosoft/os/beijingexitapply/resources/sql/create_table.sql` - 建表脚本

### 6. 视图层
- `src/main/webapp/WEB-INF/pages/os/cn/com/sinosoft/os/beijingexitapply/qryBeijingExitApply.jsp` - 查询页面
- `src/main/webapp/WEB-INF/pages/os/cn/com/sinosoft/os/beijingexitapply/edtBeijingExitApply.jsp` - 编辑页面

### 7. 工作流定义
- `src/main/java/cn/com/sinosoft/os/beijingexitapply/bpmn/beijingExitApply.bpmn` - BPMN流程定义

## 数据库字段说明

| 字段名 | 中文名称 | 数据类型 | 说明 |
|--------|----------|----------|------|
| ID | 主键ID | VARCHAR2(32) | UUID主键 |
| APPLICANT_NAME | 姓名 | VARCHAR2(50) | 申请人姓名 |
| IDENTITY | 身份 | VARCHAR2(20) | 学生/职工/中层 |
| DEPARTMENT | 科室 | VARCHAR2(100) | 自动获取用户科室 |
| APPLICATION_DATE | 申请日期 | DATE | 申请提交日期 |
| START_DATE | 起始日期 | DATE | 出京开始日期 |
| END_DATE | 结束日期 | DATE | 出京结束日期 |
| DAYS | 天数 | NUMBER(3) | 自动计算天数 |
| DESTINATION | 目的地 | VARCHAR2(200) | 出京目的地 |
| REASON | 事由 | VARCHAR2(500) | 出京事由 |
| BUSINESS_STATUS | 业务状态 | VARCHAR2(20) | 草稿/审批中/已通过/已驳回 |
| PROCESS_ID | 流程ID | VARCHAR2(64) | Activiti流程实例ID |

## 功能特性

### 1. 基本功能
- ✅ 申请信息录入（姓名、身份、科室、日期、目的地、事由等）
- ✅ 申请信息查询和列表展示
- ✅ 申请信息修改（草稿和驳回状态）
- ✅ 申请信息删除
- ✅ 申请详情查看

### 2. 工作流功能
- ✅ 根据身份自动路由审批流程
- ✅ 职工申请 → 科室负责人审批
- ✅ 中层申请 → 领导审批
- ✅ 审批通过/驳回处理

### 3. 系统集成
- ✅ 使用项目自带的commonQueryAction框架
- ✅ 集成Activiti工作流引擎
- ✅ 支持附件上传功能
- ✅ 支持Excel导出功能

## 技术架构

### 1. 框架遵循
- 完全遵循项目现有的BSP框架
- 使用commonQueryAction + queryCode模式
- 遵循BaseEditAction标准模式
- 使用项目标准的三层架构
- **Service层使用CommonBaseDao而非继承BaseDao**
- **完全参考新公章申请模块的实现模式**

### 2. DAO层配置
- Service实现类使用`CommonBaseDao`接口
- Spring配置中注入`commonBaseDaoHib`
- 同时注入`WorkflowService`用于工作流处理
- 使用现成的DAO方法：`dao.get()`, `dao.save()`, `dao.edit()`, `dao.qryBySql()`等

### 3. 查询配置
- 查询代码：`QRY_OS_BEIJING_EXIT_APPLY`
- 需要在框架中手动配置SQL查询语句
- 使用标准的分页查询模式

### 4. 权限控制
- 集成项目权限系统
- 支持按角色控制功能访问
- 支持数据级权限控制

## 部署说明

### 1. 数据库部署
```sql
-- 执行建表脚本
@src/main/java/cn/com/sinosoft/os/beijingexitapply/resources/sql/create_table.sql
```

### 2. 查询配置
需要在系统中配置查询代码 `QRY_OS_BEIJING_EXIT_APPLY` 对应的SQL语句：
```sql
SELECT 
    ID,
    APPLICANT_NAME,
    IDENTITY,
    DEPARTMENT,
    APPLICATION_DATE,
    START_DATE,
    END_DATE,
    DAYS,
    DESTINATION,
    REASON,
    BUSINESS_STATUS,
    PROCESS_ID
FROM OS_BEIJING_EXIT_APPLY 
WHERE STATE = '1'
ORDER BY APPLICATION_DATE DESC
```

### 3. 工作流部署
- 在Activiti中部署BPMN流程定义
- 配置用户组：DEPT_HEAD（科室负责人）、LEADERSHIP（领导）

### 4. 菜单配置
在系统菜单中添加出京申请模块的访问入口。

## 访问地址

- 查询页面：`/cn/com/sinosoft/os/beijingexitapply/qryBeijingExitApply.ac`
- 新增申请：`/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_addParentInput.ac`

## 注意事项

1. 所有文件已按照项目标准结构放置
2. 配置文件已正确引入到主配置中
3. 查询SQL需要通过框架手动配置
4. 工作流需要单独部署到Activiti引擎
5. 建议先在测试环境验证功能完整性

## 代码修正说明

### 重要修正
1. **Service实现类架构调整**：
   - 从继承`BaseDao`改为实现接口模式
   - 使用`CommonBaseDao`接口，注入`commonBaseDaoHib`
   - 添加`WorkflowService`依赖注入

2. **DAO方法使用**：
   - 使用现成的DAO方法：`dao.get()`, `dao.save()`, `dao.edit()`, `dao.qryBySql()`
   - 删除方法使用SQL更新而非Hibernate删除
   - 附件处理使用`dao.getAttachments()`和`dao.effectAttach()`

3. **工作流集成**：
   - 参考新公章申请的工作流处理方式
   - 使用`wfservice.handleTaskWithMap()`处理审批
   - 支持流程启动、审批通过、驳回等操作

4. **Spring配置修正**：
   - Service配置使用`scope="prototype"`
   - 注入`commonBaseDaoHib`和`WorkflowService`
   - 移除直接的`sessionFactory`注入

## 模块状态

✅ **开发完成** - 所有代码文件已创建并配置完成，已修正架构问题
⏳ **待配置** - 需要配置查询SQL和部署工作流
⏳ **待测试** - 需要在实际环境中测试功能
