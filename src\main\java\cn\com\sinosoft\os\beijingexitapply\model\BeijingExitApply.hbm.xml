<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="cn.com.sinosoft.os.beijingexitapply.model.BeijingExitApply" table="OS_BEIJING_EXIT_APPLY">
        <id name="id" column="ID" type="java.lang.String">
            <generator class="uuid"></generator>
        </id>
        <property name="applicantName" column="APPLICANT_NAME" type="java.lang.String"/>
        <property name="identityType" column="IDENTITY_TYPE" type="java.lang.String"/>
        <property name="department" column="DEPARTMENT" type="java.lang.String"/>
        <property name="applyDate" column="APPLY_DATE" type="java.util.Date"/>
        <property name="startDate" column="START_DATE" type="java.util.Date"/>
        <property name="endDate" column="END_DATE" type="java.util.Date"/>
        <property name="travelDays" column="TRAVEL_DAYS" type="java.lang.Integer"/>
        <property name="destination" column="DESTINATION" type="java.lang.String"/>
        <property name="travelReason" column="TRAVEL_REASON" type="java.lang.String"/>
        <property name="piId" column="PI_ID" type="java.lang.String"/>
        <property name="taskId" column="TASK_ID" type="java.lang.String"/>
        <property name="businessKey" column="BUSINESS_KEY" type="java.lang.String"/>
        <property name="processDefKey" column="PROCESS_DEF_KEY" type="java.lang.String"/>
        <property name="auditState" column="AUDIT_STATE" type="java.lang.String"/>
        <property name="deptLeader" column="DEPT_LEADER" type="java.lang.String"/>
        <property name="auditOpinion" column="AUDIT_OPINION" type="java.lang.String"/>
        <property name="addZone" column="ADD_ZONE" type="java.lang.String"/>
        <property name="addOrg" column="ADD_ORG" type="java.lang.String"/>
        <property name="addDep" column="ADD_DEP" type="java.lang.String"/>
        <property name="addUser" column="ADD_USER" type="java.lang.String"/>
        <property name="addTime" column="ADD_TIME" type="java.util.Date"/>
        <property name="modyZone" column="MODY_ZONE" type="java.lang.String"/>
        <property name="modyOrg" column="MODY_ORG" type="java.lang.String"/>
        <property name="modyDep" column="MODY_DEP" type="java.lang.String"/>
        <property name="modyUser" column="MODY_USER" type="java.lang.String"/>
        <property name="modyTime" column="MODY_TIME" type="java.util.Date"/>
        <property name="state" column="STATE" type="java.lang.String"/>
        <property name="dataSource" column="DATA_SOURCE" type="java.lang.String"/>
        <property name="dataModyTime" column="DATA_MODY_TIME" type="java.util.Date"/>
        <property name="extendField1" column="EXTEND_FIELD1" type="java.lang.String"/>
        <property name="extendField2" column="EXTEND_FIELD2" type="java.lang.String"/>
        <property name="extendField3" column="EXTEND_FIELD3" type="java.lang.String"/>
        <property name="extendField4" column="EXTEND_FIELD4" type="java.lang.String"/>
        <property name="extendField5" column="EXTEND_FIELD5" type="java.lang.String"/>
        <property name="versionNo" column="VERSION_NO" type="java.lang.Integer"/>
    </class>
</hibernate-mapping>
